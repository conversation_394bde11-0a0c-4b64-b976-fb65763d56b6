"use server";

import { unstable_cache } from "next/cache";

import <PERSON><PERSON> from "stripe";
import Orb from "orb-billing";
import { getObjectAclPermissions } from "#/utils/object-acl-permissions";
import { HTTPError } from "#/utils/http_error";
import {
  getPlanId,
  isMonthlyLogsIngestedGBId,
  isProPlan,
  isScoresAndCustomMetricsId,
  PLAN_SLUGS,
} from "#/app/app/[org]/settings/billing/plans";
import { otelWrapTraced } from "#/utils/tracing";

let stripe: Stripe | null = null;
let orb: Orb | null = null;

if (process.env.STRIPE_SECRET_KEY) {
  stripe = new Stripe(process.env.STRIPE_SECRET_KEY);
}

if (process.env.ORB_API_KEY) {
  orb = new Orb({
    apiKey: process.env.ORB_API_KEY,
  });
}

function hasExistingSubscriptionWithPlanId(
  subscriptions: Orb.Subscription[],
  purchasePlanId: string,
) {
  for (const sub of subscriptions) {
    if (sub.plan.id === purchasePlanId) {
      return true;
    }
  }
  return false;
}

function checkStripe(stripe: Stripe | null): asserts stripe is Stripe {
  if (!stripe) {
    throw new HTTPError(
      500,
      "Stripe is not initialized. Set the STRIPE_SECRET_KEY environment variable.",
    );
  }
}

function checkOrb(orb: Orb | null): asserts orb is Orb {
  if (!orb) {
    throw new HTTPError(
      500,
      "Orb is not initialized. Set the ORB_API_KEY environment variable.",
    );
  }
}

function checkOrgId(orgId: string | undefined): asserts orgId is string {
  if (!orgId) {
    throw new HTTPError(400, "Org ID is required");
  }
}

async function checkOrgUpdatePermission(orgId: string, errorMessage: string) {
  const orgPermissions = await getObjectAclPermissions({
    objectType: "organization",
    objectId: orgId,
  });

  if (!orgPermissions || !orgPermissions.includes("update")) {
    throw new HTTPError(403, errorMessage);
  }
}

function getCardDetails(card: Stripe.PaymentMethod.Card | Stripe.Card | null) {
  // We only want to display card details if we have all the details
  if (!card?.last4 || !card?.brand || !card?.exp_month || !card?.exp_year) {
    return null;
  }

  return {
    last4: card.last4,
    brand: card.brand,
    exp_month: card.exp_month,
    exp_year: card.exp_year,
  };
}

export type GetDefaultPaymentMethodInput = {
  stripeCustomerId: string | null;
  orgId?: string;
};

export const getDefaultPaymentMethod = otelWrapTraced(
  "getDefaultPaymentMethod",
  async ({
    stripeCustomerId,
    orgId,
  }: GetDefaultPaymentMethodInput): Promise<{
    last4: string;
    brand: string;
    exp_month: number;
    exp_year: number;
  } | null> => {
    if (!stripeCustomerId) {
      return null;
    }
    return await getDefaultPaymentMethodSummary(stripeCustomerId, orgId);
  },
);

export async function getDefaultPaymentMethodSummary(
  stripeCustomerId: string,
  orgId?: string,
) {
  checkStripe(stripe);
  checkOrgId(orgId);
  // Only allow users with the "Manage settings" permission on the organization to get the default payment method
  await checkOrgUpdatePermission(
    orgId,
    'To view billing and usage features, ask your administrator to grant the "Manage settings" permission for this organization.',
  );
  const stripeCustomer = await stripe.customers.retrieve(stripeCustomerId);

  if (stripeCustomer.deleted) {
    throw new HTTPError(404, "Customer not found");
  }

  const defaultPaymentMethod =
    stripeCustomer.invoice_settings.default_payment_method;

  if (typeof defaultPaymentMethod === "string") {
    const paymentMethod =
      await stripe.paymentMethods.retrieve(defaultPaymentMethod);
    return getCardDetails(paymentMethod.card ?? null);
  } else if (defaultPaymentMethod) {
    return getCardDetails(defaultPaymentMethod.card ?? null);
  }

  return null;
}

export type GetOrbSubscriptionInput = {
  orgId: string | null;
};

export const getOrbSubscription = otelWrapTraced(
  "getOrbSubscription",
  async ({
    orgId,
  }: GetOrbSubscriptionInput): Promise<Orb.Subscriptions.Subscription | null> => {
    if (!orgId) {
      return null;
    }
    return await getCurrentSubscription(orgId);
  },
);

export async function getCurrentSubscription(
  orgId: string,
): Promise<Orb.Subscriptions.Subscription | null> {
  checkOrb(orb);

  // Only allow users with the "Manage settings" permission on the organization to retrieve the subscription
  await checkOrgUpdatePermission(
    orgId,
    'To view billing and usage features, ask your administrator to grant the "Manage settings" permission for this organization.',
  );
  let subscriptions: Orb.Subscriptions.SubscriptionsPage | null = null;

  try {
    subscriptions = await orb.subscriptions.list({
      external_customer_id: [orgId],
      status: "active",
    });
  } catch (error) {
    if (error instanceof Error && error.message.includes("404")) {
      return null;
    } else {
      throw error;
    }
  }

  if (subscriptions.data.length === 0) {
    return null;
  }

  // If there is a data problem and there are multiple active subscriptions, return the newest one
  const sortedSubscriptions = subscriptions.data.sort((a, b) => {
    return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
  });

  return sortedSubscriptions[0];
}

export type GetOrbInvoicesInput = {
  orgId: string | null;
};

export const getOrbInvoices = otelWrapTraced(
  "getOrbInvoices",
  async ({
    orgId,
  }: GetOrbInvoicesInput): Promise<Orb.Invoices.Invoice[] | null> => {
    if (!orgId) {
      return null;
    }
    return await getInvoices({ orgId });
  },
);

export async function getInvoices({ orgId }: { orgId: string }) {
  checkOrb(orb);

  // Only allow users with the "Manage settings" permission on the organization to retrieve the subscription
  await checkOrgUpdatePermission(
    orgId,
    'To view billing and usage features, ask your administrator to grant the "Manage settings" permission for this organization.',
  );
  let invoices: Orb.Invoices.InvoicesPage | null = null;
  try {
    invoices = await orb.invoices.list({
      external_customer_id: orgId,
    });
  } catch (error) {
    if (error instanceof Error && error.message.includes("404")) {
      return null;
    } else {
      throw error;
    }
  }

  if (invoices.data.length === 0) {
    return null;
  }

  // TODO: pagination support
  return invoices.data;
}

async function getOrCreateStripeCustomer({
  orgId,
  orgName,
  userEmail,
}: {
  orgId: string;
  orgName: string;
  userEmail: string;
}): Promise<Stripe.Customer> {
  checkOrgId(orgId);
  checkStripe(stripe);

  const stripeCustomerSearch = await stripe.customers.search({
    query: `metadata['orgId']:\'${orgId}\'`,
  });

  if (stripeCustomerSearch.data.length > 0) {
    return stripeCustomerSearch.data[0];
  }

  return await stripe.customers.create({
    name: orgName,
    email: userEmail,
    metadata: {
      orgId,
    },
  });
}

export async function getOrbCustomer({ orgId }: { orgId: string }) {
  checkOrb(orb);
  return await orb.customers.fetchByExternalId(orgId);
}

export async function getOrCreateOrbCustomer({
  orgId,
  orgName,
  userEmail,
}: {
  orgId: string;
  orgName: string;
  userEmail: string;
}): Promise<Orb.Customer> {
  checkOrb(orb);

  try {
    const orbCustomer = await orb.customers.fetchByExternalId(orgId);

    return orbCustomer;
  } catch (error) {
    if (error instanceof Error && error.message.includes("404")) {
      return await orb.customers.create({
        name: orgName,
        email: userEmail,
        external_customer_id: orgId,
      });
    }
    throw error;
  }
}

export async function maybeCreateOrbSubscription({
  orbCustomerId,
  purchasePlanId,
  couponCode,
  isPending,
}: {
  orbCustomerId: string;
  purchasePlanId?: string;
  couponCode?: string | null;
  isPending?: boolean;
}): Promise<Orb.Subscription | null> {
  checkOrb(orb);

  if (!purchasePlanId) {
    return null;
  }

  const subscriptionSearch = await orb.subscriptions.list({
    customer_id: [orbCustomerId],
  });

  // If we have an existing subscription, and the purchase plan id is the same as the existing subscription,
  // we don't need to create a new subscription
  if (
    hasExistingSubscriptionWithPlanId(subscriptionSearch.data, purchasePlanId)
  ) {
    return null;
  }

  // If we have an existing subscription, and the purchase plan id is different than the existing subscription,
  // we need to schedule a plan change
  if (subscriptionSearch.data.length > 0) {
    const existingSubscription = subscriptionSearch.data[0];
    if (existingSubscription.plan.id !== purchasePlanId) {
      // We are about to create a pending subscription change, so we need to cancel any existing pending changes
      const existingPendingChangeId =
        // @ts-expect-error - pending_subscription_change is not typed in the orb node module, it's brand new
        existingSubscription.pending_subscription_change?.id;

      if (existingPendingChangeId) {
        await cancelPendingSubscriptionChange(existingPendingChangeId);
      }

      return await orb.subscriptions.schedulePlanChange(
        existingSubscription.id,
        {
          change_option: "immediate",
          plan_id: purchasePlanId,
          coupon_redemption_code: couponCode ?? undefined,
        },
        isPending
          ? { headers: { "Create-Pending-Subscription-Change": "true" } }
          : undefined,
      );
    }
  }

  // If there is no existing subscription, create a new one
  return await orb.subscriptions.create(
    {
      customer_id: orbCustomerId,
      plan_id: purchasePlanId,
      auto_collection: true,
      coupon_redemption_code: couponCode ?? undefined,
    },
    isPending
      ? { headers: { "Create-Pending-Subscription-Change": "true" } }
      : undefined,
  );
}

async function validateCouponCode(
  couponCode: string,
): Promise<{ isValid: boolean }> {
  checkOrb(orb);

  const coupons = await orb.coupons.list({
    redemption_code: couponCode,
  });

  if (coupons.data.length === 0) {
    return { isValid: false };
  }

  const coupon = coupons.data[0];
  const isRedemptionsOK =
    coupon.max_redemptions === null ||
    coupon.max_redemptions > coupon.times_redeemed;

  return {
    isValid: !coupon.archived_at && isRedemptionsOK,
  };
}

async function maybeUpdateStripeCustomerEmail({
  email,
  stripeCustomerId,
}: {
  email: string;
  stripeCustomerId: string;
}) {
  checkStripe(stripe);
  try {
    const stripeCustomer = await stripe.customers.retrieve(stripeCustomerId);
    if (stripeCustomer.deleted) {
      throw new HTTPError(404, "Customer not found");
    }
    await stripe.customers.update(stripeCustomerId, {
      email: email,
    });
  } catch (error) {
    console.error("Error updating stripe customer email:", error);
    throw new HTTPError(500, "Failed to update stripe customer email");
  }
}

/**
 *  Update the billing email for the Orb customer and stripe customer associated with the given organization.
 *  Orb customer is always updated first, then the stripe customer.
 */
export async function addOrbCustomerBillingEmail({
  orgId,
  email,
}: {
  orgId: string;
  email: string;
}) {
  checkOrb(orb);
  checkStripe(stripe);

  // Only allow users with the "Manage settings" permission on the organization to add a payment intent
  await checkOrgUpdatePermission(
    orgId,
    'You are not authorized to add a payment method. To use billing and usage features, ask your administrator to grant the "Manage settings" permission for this organization.',
  );

  let orbCustomer = await getOrbCustomer({
    orgId,
  });
  try {
    orbCustomer = await orb.customers.update(orbCustomer.id, {
      email: email,
    });

    const stripeCustomerId = orbCustomer.payment_provider_id;
    if (stripeCustomerId) {
      await maybeUpdateStripeCustomerEmail({ email, stripeCustomerId });
    }

    return { email: orbCustomer.email };
  } catch (error) {
    console.error("Error updating customer:", error);
    throw new HTTPError(500, "Failed to update customer email");
  }
}

export async function addPaymentIntent({
  orgId,
  orgName,
  userEmail,
  couponCode,
}: {
  orgId: string | undefined;
  orgName: string;
  userEmail: string;
  couponCode?: string | null;
}) {
  checkOrgId(orgId);
  checkStripe(stripe);
  checkOrb(orb);

  // Only allow users with the "Manage settings" permission on the organization to add a payment intent
  await checkOrgUpdatePermission(
    orgId,
    'You are not authorized to add a payment method. To use billing and usage features, ask your administrator to grant the "Manage settings" permission for this organization.',
  );

  try {
    // Create or retrieve the Orb customer
    const orbCustomer = await getOrCreateOrbCustomer({
      orgId,
      orgName,
      userEmail,
    });

    const billingEmail = orbCustomer.email || userEmail;

    // Create or retrieve the Stripe customer
    const stripeCustomer = await getOrCreateStripeCustomer({
      orgId,
      orgName,
      userEmail: billingEmail,
    });

    const setupIntent = await stripe.setupIntents.create({
      customer: stripeCustomer.id,
      payment_method_types: ["card"],
      metadata: {
        orgId,
      },
    });

    await orb.customers.update(orbCustomer.id, {
      payment_provider: "stripe_charge",
      payment_provider_id: stripeCustomer.id,
      currency: "USD",
    });

    // If a coupon code is provided, validate it before proceeding
    if (couponCode) {
      const { isValid } = await validateCouponCode(couponCode);
      if (!isValid) {
        throw new HTTPError(400, "Invalid coupon code");
      }
    }

    return {
      clientSecret: setupIntent.client_secret,
      orbCustomerId: orbCustomer.id,
    };
  } catch (error) {
    console.error("Error creating customer:", error);
    throw new HTTPError(500, "Failed to create customer");
  }
}

export async function getSetupIntent(setupIntentId: string) {
  checkStripe(stripe);

  return await stripe.setupIntents.retrieve(setupIntentId);
}

export async function setDefaultPaymentMethod(setupIntent: Stripe.SetupIntent) {
  checkStripe(stripe);

  try {
    const paymentMethodId = setupIntent.payment_method;
    const customerId = setupIntent.customer;

    if (typeof paymentMethodId !== "string" || typeof customerId !== "string") {
      throw new HTTPError(400, "No payment method or customer found");
    }

    await stripe.customers.update(customerId, {
      invoice_settings: {
        default_payment_method: paymentMethodId,
      },
    });
  } catch (error) {
    console.error("Error setting default payment method:", error);
    throw new HTTPError(500, "Failed to set default payment method");
  }
}

export async function syncPaymentMethodInOrb({ orgId }: { orgId: string }) {
  checkOrb(orb);

  await orb.customers.syncPaymentMethodsFromGatewayByExternalCustomerId(orgId);
}

export async function updateOrbCustomerAddressFromStripeSetupIntent({
  setupIntent,
  orbCustomerId,
}: {
  setupIntent: Stripe.SetupIntent;
  orbCustomerId: string;
}) {
  checkOrb(orb);

  // Extract address information from the payment method's billing details
  const paymentMethodId = setupIntent.payment_method;
  if (typeof paymentMethodId !== "string") {
    return; // No payment method to extract address from
  }

  // Retrieve the payment method to get billing details
  checkStripe(stripe);
  const paymentMethod = await stripe.paymentMethods.retrieve(paymentMethodId);

  const billingDetails = paymentMethod.billing_details;
  if (!billingDetails?.address) {
    return; // No address information available
  }

  const address = billingDetails.address;

  // Build address object for Orb customer update
  const addressUpdate: {
    line1?: string;
    line2?: string;
    city?: string;
    state?: string;
    postal_code?: string;
    country?: string;
  } = {};

  if (address.line1) addressUpdate.line1 = address.line1;
  if (address.line2) addressUpdate.line2 = address.line2;
  if (address.city) addressUpdate.city = address.city;
  if (address.state) addressUpdate.state = address.state;
  if (address.postal_code) addressUpdate.postal_code = address.postal_code;
  if (address.country) addressUpdate.country = address.country;

  // Only update if we have at least some address information
  if (Object.keys(addressUpdate).length > 0) {
    await orb.customers.update(orbCustomerId, {
      billing_address: addressUpdate,
    });
  }
}

export async function validateCoupon(args: { couponCode: string }) {
  const { couponCode } = args;
  if (!couponCode) {
    throw new Error("Missing coupon code");
  }

  try {
    return await validateCouponCode(couponCode);
  } catch (error) {
    return { isValid: false };
  }
}

export async function orbFetch({
  path,
  method,
  body,
}: {
  path: string;
  method: string;
  body?: unknown;
}) {
  checkOrb(orb);

  const options = {
    method,
    headers: { Authorization: `Bearer ${process.env.ORB_API_KEY}` },
    body: body ? JSON.stringify(body) : undefined,
  };

  const orbResponse = await fetch(
    `https://api.withorb.com/v1/${path}`,
    options,
  );

  if (!orbResponse.ok) {
    throw new HTTPError(orbResponse.status, "Failed to fetch from Orb");
  }

  const orbResponseJson = await orbResponse.json();
  return orbResponseJson;
}

// Temporary type for the Orb.PendingSubscriptionChange object
// Orb has not yet published the type for this object
type OrbPendingSubscriptionChange = {
  subscription?: {
    changed_resources?: {
      created_invoices?: Orb.Invoice[];
    };
  };
};

export async function getPendingSubscriptionChange(
  changeId: string,
): Promise<OrbPendingSubscriptionChange> {
  return orbFetch({ path: `subscription_changes/${changeId}`, method: "GET" });
}

export async function applyPendingSubscriptionChange({
  changeId,
  previouslyCollectedAmount,
}: {
  changeId: string;
  previouslyCollectedAmount: string;
}) {
  return orbFetch({
    path: `subscription_changes/${changeId}/apply`,
    method: "POST",
    body: {
      previously_collected_amount: previouslyCollectedAmount,
      description: "Stripe PaymentIntent confirmed",
    },
  });
}

export async function cancelPendingSubscriptionChange(changeId: string) {
  return orbFetch({
    path: `subscription_changes/${changeId}/cancel`,
    method: "POST",
  });
}

async function createAndConfirmPayment({
  amount,
  customerId,
}: {
  amount: number;
  customerId: string;
}) {
  checkStripe(stripe);

  // Get the customer's default payment method
  const customer = await stripe.customers.retrieve(customerId);
  if (customer.deleted) {
    throw new Error("Customer not found");
  }

  const defaultPaymentMethodId =
    typeof customer.invoice_settings.default_payment_method === "string"
      ? customer.invoice_settings.default_payment_method
      : null;

  if (!defaultPaymentMethodId) {
    throw new Error("No default payment method found");
  }

  const paymentIntent = await stripe.paymentIntents.create({
    amount,
    currency: "usd",
    customer: customerId,
    payment_method: defaultPaymentMethodId,
    confirm: true,
    automatic_payment_methods: {
      enabled: true,
      allow_redirects: "never",
    },
  });

  return {
    status: paymentIntent.status,
    succeeded: paymentIntent.status === "succeeded",
    error: paymentIntent.last_payment_error
      ? {
          message: paymentIntent.last_payment_error.message,
          code: paymentIntent.last_payment_error.code,
          type: paymentIntent.last_payment_error.type,
          decline_code: paymentIntent.last_payment_error.decline_code,
        }
      : null,
  };
}

async function findInvoiceToBillNow(pendingSubscriptionChangeId: string) {
  const pendingSubscriptionChange = await getPendingSubscriptionChange(
    pendingSubscriptionChangeId,
  );

  const createdInvoices: Orb.Invoice[] | undefined =
    pendingSubscriptionChange?.subscription?.changed_resources
      ?.created_invoices;

  if (!createdInvoices) {
    return undefined;
  }

  const currentMonth = new Date().getMonth();
  const currentYear = new Date().getFullYear();

  // The invoice to bill now is the invoice with the earliest invoice_date, in the current month, that is not 0.00 and has auto collection enabled
  const nonZeroInvoices = createdInvoices
    .filter(
      (invoice) =>
        invoice.total !== "0.00" &&
        invoice.auto_collection.enabled &&
        new Date(invoice.invoice_date).getMonth() === currentMonth &&
        new Date(invoice.invoice_date).getFullYear() === currentYear,
    )
    .sort(
      (a, b) =>
        new Date(a.invoice_date).getTime() - new Date(b.invoice_date).getTime(),
    );

  if (nonZeroInvoices.length === 0) {
    return undefined;
  }

  return nonZeroInvoices[0];
}

export async function createAndConfirmPaymentFromPendingSubscription({
  pendingSubscription,
  setupIntent,
}: {
  pendingSubscription: Orb.Subscription;
  setupIntent: Stripe.SetupIntent;
}) {
  checkOrb(orb);

  const pendingSubscriptionChangeId =
    // @ts-expect-error - pending_subscription_change is not typed in the orb node module, it's brand new
    pendingSubscription.pending_subscription_change?.id;

  if (!pendingSubscriptionChangeId) {
    throw new Error("Unexpectedly no pending subscription");
  }

  if (!setupIntent.customer) {
    throw new Error("Problem with Stripe intent: customer not found");
  }

  const customerId =
    typeof setupIntent.customer === "string"
      ? setupIntent.customer
      : setupIntent.customer.id;

  const invoiceToBillNow = await findInvoiceToBillNow(
    pendingSubscriptionChangeId,
  );

  if (!invoiceToBillNow) {
    // It's possible that the invoice to bill is not found because of a coupon or other discount
    return {
      pendingSubscriptionChangeId,
      previouslyCollectedAmount: "0.00",
    };
  }

  // Convert dollar amount string to cents for Stripe
  const amountInCents = Math.round(
    parseFloat(invoiceToBillNow.amount_due) * 100,
  );

  const { succeeded, error: stripePaymentError } =
    await createAndConfirmPayment({
      amount: amountInCents,
      customerId,
    });

  if (!succeeded) {
    const stripeErrorMessage =
      stripePaymentError?.message ||
      "Payment failed. Please check your card details and try again.";
    throw new Error(
      `Failed to create and confirm payment. Reasons: ${stripeErrorMessage}`,
    );
  }

  return {
    pendingSubscriptionChangeId,
    previouslyCollectedAmount: invoiceToBillNow.amount_due,
  };
}

export type UsageData = {
  currentUsage: number;
  limit: number;
  percentageUsed: number;
};

function getCurrentMonthTimeframe() {
  const now = new Date();
  const firstDay = new Date(now.getFullYear(), now.getMonth(), 1);
  const lastDay = new Date(
    now.getFullYear(),
    now.getMonth() + 1,
    0,
    23,
    59,
    59,
    999,
  );

  return {
    start: firstDay.toISOString(),
    end: lastDay.toISOString(),
  };
}

const getCachedUsage = unstable_cache(
  async (
    orgId: string,
  ): Promise<{
    logs: UsageData | null;
    scores: UsageData | null;
    customerPortalUrl: string | null;
  } | null> => {
    if (!orb) {
      return null;
    }

    try {
      const subscription = await getCurrentSubscription(orgId);

      if (!subscription) {
        return null;
      }

      const { start: timeframeStart, end: timeframeEnd } =
        getCurrentMonthTimeframe();

      const usageResponse = await orb.subscriptions.fetchUsage(
        subscription.id,
        { timeframe_start: timeframeStart, timeframe_end: timeframeEnd },
      );

      const logsMetric = usageResponse.data?.find((u) =>
        isMonthlyLogsIngestedGBId(u.billable_metric.id),
      );

      const scoresMetric = usageResponse.data?.find((u) =>
        isScoresAndCustomMetricsId(u.billable_metric.id),
      );

      const usageData: {
        logs: UsageData | null;
        scores: UsageData | null;
        customerPortalUrl: string | null;
      } = {
        logs: null,
        scores: null,
        customerPortalUrl: subscription.customer.portal_url,
      };

      if (logsMetric) {
        const currentUsage = logsMetric.usage[0].quantity || 0;
        usageData.logs = {
          currentUsage,
          limit: 1,
          percentageUsed: Math.min((currentUsage / 1) * 100, 100),
        };
      }

      if (scoresMetric) {
        const currentUsage = scoresMetric.usage[0].quantity || 0;
        usageData.scores = {
          currentUsage,
          limit: 10000,
          percentageUsed: Math.min((currentUsage / 10000) * 100, 100),
        };
      }

      return usageData;
    } catch (error) {
      // If any Error comes back from Orb, we just won't show the usage chart
      // Don't show an error toast or anything, we don't want dev environments to break if Orb isn't configured
      return null;
    }
  },
  ["orb-usage"],
  {
    revalidate: 600, // Cache for 10 minutes
  },
);

export async function getCurrentUsage({ orgId }: { orgId: string }): Promise<{
  logs: UsageData | null;
  scores: UsageData | null;
  customerPortalUrl: string | null;
} | null> {
  try {
    // Only allow users with the "Update" permission on the organization to get current usage
    await checkOrgUpdatePermission(orgId, "Not authorized to get usage data");
  } catch (e) {
    console.error("Error fetching usage data:", e);
    return null;
  }

  return getCachedUsage(orgId);
}

export async function downgradeProSubscription({ orgId }: { orgId: string }) {
  checkOrb(orb);

  // Only allow users with the "Update" permission on the organization to downgrade the subscription
  await checkOrgUpdatePermission(
    orgId,
    "You are not authorized to downgrade the subscription",
  );

  // Ensure that that the current subscription is a pro subscription
  const subscription = await getCurrentSubscription(orgId);

  if (!subscription) {
    throw new HTTPError(404, "Subscription not found");
  }

  if (!isProPlan(subscription.plan.id)) {
    throw new HTTPError(400, "Current subscription is not a pro subscription");
  }

  // Move the subscription to the free plan immediately
  return await orb.subscriptions.schedulePlanChange(subscription.id, {
    plan_id: getPlanId(PLAN_SLUGS.FREE),
    change_option: "immediate",
  });
}
