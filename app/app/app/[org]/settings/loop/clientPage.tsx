"use client";

import { useAvailableModels } from "#/ui/prompts/models";
import { type Permission } from "@braintrust/typespecs";
import LoopModelWhitelist from "./loop-model-whitelist";
import { useOrg } from "#/utils/user";
import { Skeleton } from "#/ui/skeleton";
import { useQueryFunc } from "#/utils/react-query";
import { type fetchLoopModelWhitelist } from "#/app/app/[org]/settings/loop/actions";
import { ErrorBanner } from "#/ui/error-banner";
import { useIsFeatureEnabled } from "#/lib/feature-flags";
import { InfoBanner } from "#/ui/info-banner";
import Link from "next/link";
import { getOrgSettingsLink } from "../../getOrgLink";

interface ClientPageProps {
  orgName: string;
  orgPermissions: Permission[];
}

const ClientPage = ({ orgName, orgPermissions }: ClientPageProps) => {
  const org = useOrg();
  const { configuredModelsByProvider } = useAvailableModels({ orgName });
  const isLoopEnabled = useIsFeatureEnabled("loop");
  const disabled = !orgPermissions.includes("update");

  const {
    data: fetchedLoopAllowedModels,
    error: whitelistError,
    isLoading: isWhitelistLoading,
    invalidate: invalidateWhitelist,
  } = useQueryFunc<typeof fetchLoopModelWhitelist>({
    fName: "fetchLoopModelWhitelist",
    args: { orgId: org.id },
    queryOptions: {
      enabled: !!org.id,
    },
  });

  if (!isLoopEnabled) {
    return (
      <div>
        <div className="mb-4 flex w-full items-end justify-between">
          <div className="flex flex-col">
            <h2 className="text-base font-semibold">Loop model whitelist</h2>
            <p className="pt-2 text-sm text-primary-700">
              Configure which models your organization can use in Loop. By
              default, a recommended set of models are selected.
            </p>
          </div>
        </div>
        <InfoBanner>
          Loop is not enabled for this organization. To enable Loop, turn on the
          feature flag in the{" "}
          <Link
            href={getOrgSettingsLink({ orgName }) + "/feature-flags"}
            className="underline"
          >
            feature flags page
          </Link>
          .
        </InfoBanner>
      </div>
    );
  }

  return (
    <div>
      <div className="mb-4 flex w-full items-end justify-between">
        <div className="flex flex-col">
          <h2 className="text-base font-semibold">Loop model whitelist</h2>
          <p className="pt-2 text-sm text-primary-700">
            Configure which models your organization can use in Loop. By
            default, a recommended set of models are selected.
          </p>
        </div>
      </div>
      {isWhitelistLoading && <Skeleton className="h-[320px] w-full" />}
      {whitelistError && (
        <ErrorBanner skipErrorReporting>{whitelistError.message}</ErrorBanner>
      )}
      {!isWhitelistLoading && !whitelistError && org.id && (
        <LoopModelWhitelist
          fetchedLoopAllowedModels={fetchedLoopAllowedModels || null}
          orgName={orgName}
          orgId={org.id}
          modelOptionsByProvider={configuredModelsByProvider}
          disabled={disabled}
          invalidateWhitelist={invalidateWhitelist}
        />
      )}
    </div>
  );
};

export default ClientPage;
