import { CollapsibleSection } from "#/ui/collapsible-section";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, Di<PERSON>Header, DialogTitle } from "#/ui/dialog";
import {
  PRIORITIZED_DEFAULT_MODELS,
  isModelMatch,
} from "#/ui/optimization/model-utils";
import { Checkbox, IndeterminateCheckbox } from "#/ui/checkbox";
import { Label } from "#/ui/label";
import { Button } from "#/ui/button";
import { getProviderConfig, providerReadableName } from "../secrets/utils";
import { type LoopAllowedModels } from "#/app/app/[org]/settings/loop/actions";
import { useEffect, useMemo, useState } from "react";
import { type ModelDetails } from "#/ui/prompts/models";
import { Tooltip, TooltipContent, TooltipTrigger } from "#/ui/tooltip";
import { type ModelItem } from "./loop-model-whitelist";
import { Input } from "#/ui/input";

export const makeKey = (provider: string, modelName: string) =>
  `${provider}::${modelName}`;
export const parseKey = (key: string) => {
  const [provider, modelName] = key.split("::");
  return { provider, modelName };
};

const WhitelistEditDialog = ({
  modelOptionsByProvider,
  loopAllowedModels,
  addModelOpen,
  setAddModelOpen,
  onSave,
}: {
  modelOptionsByProvider: Record<string, ModelDetails[]>;
  loopAllowedModels: ModelItem[];
  addModelOpen: boolean;
  setAddModelOpen: (open: boolean) => void;
  onSave: (newWhitelist: LoopAllowedModels) => void | Promise<void>;
}) => {
  const [draftSelected, setDraftSelected] = useState<Set<string>>(new Set());
  const [saving, setSaving] = useState(false);
  const [search, setSearch] = useState("");

  const initialSelected = useMemo(() => {
    return new Set(
      loopAllowedModels.map((m) => makeKey(m.provider, m.modelName)),
    );
  }, [loopAllowedModels]);

  useEffect(() => {
    if (!addModelOpen) return;
    setDraftSelected(initialSelected);
  }, [addModelOpen, initialSelected]);

  const toggleModel = (provider: string, modelName: string) => {
    setDraftSelected((prev) => {
      const next = new Set(prev);
      const key = makeKey(provider, modelName);
      if (next.has(key)) {
        next.delete(key);
      } else {
        next.add(key);
      }
      return next;
    });
  };

  const handleSave = async () => {
    setSaving(true);
    try {
      const draftEqualsInitial =
        draftSelected.size === initialSelected.size &&
        [...draftSelected].every((n) => initialSelected.has(n));

      if (!draftEqualsInitial) {
        const newValue = Array.from(draftSelected).map((key) => {
          return parseKey(key);
        });
        await onSave(newValue);
      }
    } finally {
      setSaving(false);
      setAddModelOpen(false);
    }
  };

  const allModelsCount = useMemo(() => {
    return Object.values(modelOptionsByProvider).reduce(
      (sum, models) => sum + models.filter((model) => !model.deprecated).length,
      0,
    );
  }, [modelOptionsByProvider]);

  const allSelectedCount = draftSelected.size;
  const globalState: "checked" | "unchecked" | "indeterminate" =
    allSelectedCount === 0
      ? "unchecked"
      : allSelectedCount === allModelsCount
        ? "checked"
        : "indeterminate";

  const addedModels = [...draftSelected].filter(
    (name) => !initialSelected.has(name),
  );
  const removedModels = [...initialSelected].filter(
    (name) => !draftSelected.has(name),
  );

  const changesByProvider = useMemo(() => {
    type ChangeItem = { details: ModelDetails; change: "added" | "removed" };
    const grouped: Record<string, ChangeItem[]> = {};
    for (const key of addedModels) {
      const { provider, modelName } = parseKey(key);
      const details = modelOptionsByProvider[provider]?.find(
        (m) => m.modelName === modelName,
      );
      if (details) {
        (grouped[provider] ??= []).push({ details, change: "added" });
      }
    }
    for (const key of removedModels) {
      const { provider, modelName } = parseKey(key);
      const details = modelOptionsByProvider[provider]?.find(
        (m) => m.modelName === modelName,
      );
      if (details) {
        (grouped[provider] ??= []).push({ details, change: "removed" });
      }
    }
    return grouped;
  }, [addedModels, removedModels, modelOptionsByProvider]);

  return (
    <Dialog open={addModelOpen} onOpenChange={setAddModelOpen}>
      <DialogContent className="gap-0 p-0">
        <DialogHeader className="mb-4 px-4 pt-4">
          <DialogTitle>Edit loop model whitelist</DialogTitle>
        </DialogHeader>
        <div className="px-4">
          <Input
            placeholder="Search"
            className="h-8"
            value={search}
            onChange={(e) => setSearch(e.target.value)}
          />
        </div>
        <div className="mb-1 border-b px-4 py-2">
          <Label className="flex items-center gap-2">
            <IndeterminateCheckbox
              className="mr-1"
              state={globalState}
              onClick={() => {
                if (globalState === "checked") {
                  setDraftSelected(new Set());
                } else {
                  const allKeys = new Set<string>();
                  for (const [provider, models] of Object.entries(
                    modelOptionsByProvider,
                  )) {
                    for (const model of models) {
                      if (!model.deprecated) {
                        allKeys.add(makeKey(provider, model.modelName));
                      }
                    }
                  }
                  setDraftSelected(allKeys);
                }
              }}
            />
            Select all
            <span className="ml-auto text-xs text-primary-500">
              {" "}
              {allSelectedCount} of {allModelsCount}
            </span>
          </Label>
        </div>
        <div className="flex max-h-[500px] flex-col gap-1 overflow-y-auto px-2 pb-1">
          {Object.entries(modelOptionsByProvider).map(([provider, models]) => {
            const { Icon: ProviderIcon } = getProviderConfig(provider);
            const selectedModelNames = draftSelected;
            const selectedCount = models.reduce((count, model) => {
              return (
                count +
                (selectedModelNames.has(makeKey(provider, model.modelName)) &&
                !model.deprecated
                  ? 1
                  : 0)
              );
            }, 0);
            const providerState: "checked" | "unchecked" | "indeterminate" =
              selectedCount === 0
                ? "unchecked"
                : selectedCount === models.length
                  ? "checked"
                  : "indeterminate";
            return (
              <div key={provider}>
                <CollapsibleSection
                  key={provider}
                  titleContainerClassName="sticky top-0 bg-background"
                  className="mb-0 bg-background px-2 hover:px-2"
                  title={
                    <div className="flex items-center gap-1 font-normal">
                      <IndeterminateCheckbox
                        state={providerState}
                        onClick={(e) => {
                          e.stopPropagation();
                          if (providerState === "checked") {
                            setDraftSelected((prev) => {
                              const next = new Set(prev);
                              models.forEach((model) => {
                                next.delete(makeKey(provider, model.modelName));
                              });
                              return next;
                            });
                          } else {
                            // Check all models for this provider
                            setDraftSelected((prev) => {
                              const next = new Set(prev);
                              models.forEach((model) => {
                                next.add(makeKey(provider, model.modelName));
                              });
                              return next;
                            });
                          }
                        }}
                        className="mr-1"
                      />
                      <ProviderIcon size={16} />
                      <span className="text-sm text-primary-900">
                        {providerReadableName(provider)}
                      </span>
                    </div>
                  }
                  defaultCollapsed={false}
                >
                  <div className="ml-4 flex flex-col gap-1 border-l px-2 pt-1 pl-4">
                    {models
                      .filter(
                        (model) =>
                          (model.displayName
                            ?.toLowerCase()
                            .includes(search.toLowerCase()) ||
                            model.modelName
                              .toLowerCase()
                              .includes(search.toLowerCase()) ||
                            provider
                              .toLowerCase()
                              .includes(search.toLowerCase())) &&
                          !model.deprecated,
                      )
                      .map((model) => {
                        const isChecked = selectedModelNames.has(
                          makeKey(provider, model.modelName),
                        );
                        return (
                          <Label
                            className="flex w-full items-center justify-between gap-2"
                            key={provider + model.modelName}
                          >
                            <Checkbox
                              checked={isChecked}
                              onChange={() =>
                                toggleModel(provider, model.modelName)
                              }
                            />
                            <div className="flex w-full items-center justify-between gap-2">
                              <span className="text-sm">
                                {model.displayName || model.modelName}
                              </span>
                              {PRIORITIZED_DEFAULT_MODELS.some((defaultModel) =>
                                isModelMatch(
                                  model.modelName,
                                  defaultModel,
                                  provider,
                                ),
                              ) ? (
                                <span className="text-xs text-accent-500">
                                  Recommended{" "}
                                </span>
                              ) : model.deprecated ? (
                                <span className="text-xs text-primary-400">
                                  Deprecated
                                </span>
                              ) : model.experimental ? (
                                <span className="text-xs text-primary-400">
                                  Experimental
                                </span>
                              ) : (
                                ""
                              )}
                            </div>
                          </Label>
                        );
                      })}
                  </div>
                </CollapsibleSection>
              </div>
            );
          })}
        </div>
        <div className="flex items-center justify-end gap-2 border-t bg-primary-50 px-4 py-2">
          {(addedModels.length > 0 || removedModels.length > 0) && (
            <Tooltip delayDuration={0}>
              <TooltipTrigger asChild>
                <div className="mr-auto flex items-center gap-2 rounded-md border pl-2">
                  <div className="flex flex-col text-xs text-primary-600">
                    {(addedModels.length > 0 || removedModels.length > 0) && (
                      <div>
                        {addedModels.length > 0 && (
                          <span className="text-good-700">
                            +{addedModels.length}
                          </span>
                        )}
                        {removedModels.length > 0 && (
                          <span className="ml-1 text-bad-700">
                            -{removedModels.length}
                          </span>
                        )}
                      </div>
                    )}
                  </div>
                  <Button
                    variant="ghost"
                    size="xs"
                    onClick={() => setDraftSelected(initialSelected)}
                  >
                    Clear
                  </Button>
                </div>
              </TooltipTrigger>
              <TooltipContent className="flex max-w-[360px] min-w-[240px] flex-col gap-1 p-2">
                <span className="text-xs font-medium text-primary-700">
                  Change overview
                </span>
                {(addedModels.length > 0 || removedModels.length > 0) && (
                  <div className="mt-1 flex flex-col gap-2 text-xs">
                    {Object.entries(changesByProvider).map(
                      ([provider, items]) => {
                        const { Icon: ProviderIcon } =
                          getProviderConfig(provider);
                        return (
                          <div
                            key={"changes-" + provider}
                            className="flex flex-col gap-2"
                          >
                            <div className="flex items-center gap-1">
                              <ProviderIcon size={12} />
                              <span className="font-medium">
                                {providerReadableName(provider)}
                              </span>
                            </div>
                            <div className="flex flex-wrap gap-1">
                              {items.map(({ details, change }) => (
                                <span
                                  key={change + "-" + details.modelName}
                                  className={
                                    change === "added"
                                      ? "ml-1 w-fit rounded border border-green-200 bg-green-50 px-1 py-px text-green-700"
                                      : "ml-1 w-fit rounded border border-red-200 bg-red-50 px-1 py-px text-red-700 line-through"
                                  }
                                >
                                  {details.displayName ?? details.modelName}
                                </span>
                              ))}
                            </div>
                          </div>
                        );
                      },
                    )}
                  </div>
                )}
              </TooltipContent>
            </Tooltip>
          )}
          <Button
            variant="border"
            size="sm"
            onClick={() => setAddModelOpen(false)}
            disabled={saving}
          >
            Cancel
          </Button>
          <Button
            variant="primary"
            size="sm"
            onClick={handleSave}
            disabled={
              saving || (addedModels.length === 0 && removedModels.length === 0)
            }
            isLoading={saving}
          >
            Save
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default WhitelistEditDialog;
