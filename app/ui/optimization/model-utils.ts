import { type LoopAllowedModels } from "#/app/app/[org]/settings/loop/actions";
import { type ModelItem } from "#/app/app/[org]/settings/loop/loop-model-whitelist";
import { type ModelDetails } from "#/ui/prompts/models";

// Prioritized list of models to try as default, in order of preference
export const PRIORITIZED_DEFAULT_MODELS = [
  "claude-sonnet-4",
  "gpt-4.1",
  "claude-opus-4-1",
  "gpt-5",
  "o3",
  "o4-mini",
  "claude-3-5-sonnet",
];

//Return passed in models by provider, filtered by the whitelist. if no whitelist, just the raw passed models by provider.
export function filterModelsByWhitelist(
  modelsByProvider: Record<string, ModelDetails[]>,
  loopAllowedModels: LoopAllowedModels,
  allowOtherModels: boolean = false,
): Record<string, ModelDetails[]> {
  if (
    loopAllowedModels === null ||
    loopAllowedModels === undefined ||
    loopAllowedModels.length === 0
  ) {
    return modelsByProvider;
  }

  // If allowOtherModels flag is enabled, return all models (bypass whitelist)
  if (allowOtherModels) {
    return modelsByProvider;
  }

  if (Array.isArray(loopAllowedModels)) {
    const allowedByProvider = normalizeLoopAllowedModels(loopAllowedModels);

    const filterModelsForProvider = (
      provider: string,
      models: ModelDetails[],
    ): ModelDetails[] => {
      const allowedSet = allowedByProvider.get(provider);
      if (!allowedSet) return [];

      return models
        .map((model) => {
          if (allowedSet.has(model.modelName)) {
            return model;
          }
          return null;
        })
        .filter((m): m is ModelDetails => m !== null);
    };

    return Object.fromEntries(
      Object.entries(modelsByProvider)
        .map(([provider, models]) => [
          provider,
          filterModelsForProvider(provider, models),
        ])
        .filter(([, models]) => models.length > 0),
    );
  }

  return modelsByProvider;
}

// This is a hack to handle the fact that if we do includes on model name for OpenAI, it will pull in a bunch of mini and pro versions.
// We check for Azure as well since Azure provides OpenAI models.
// We want to do includes for Claude because there are multiple providers that offer the model but have different names
export const isModelMatch = (
  modelName: string,
  defaultModel: string,
  provider: string,
) => {
  return provider === "openai" || provider === "azure"
    ? modelName === defaultModel
    : modelName.includes(defaultModel);
};

// Get the effective prioritized models list - use whitelist if configured, otherwise defaults to our defined list
export function getEffectivePrioritizedModels(
  loopAllowedModels: LoopAllowedModels,
): string[] {
  if (
    loopAllowedModels === null ||
    loopAllowedModels === undefined ||
    loopAllowedModels.length === 0
  ) {
    return PRIORITIZED_DEFAULT_MODELS;
  }
  if (Array.isArray(loopAllowedModels)) {
    return loopAllowedModels.map((e) => e.modelName);
  }
  return PRIORITIZED_DEFAULT_MODELS;
}

export const isModelSupported = (
  model: string,
  allowOtherModels: boolean,
  availableModels: ModelDetails[],
  loopAllowedModels: LoopAllowedModels = null,
) => {
  if (!model) {
    return false;
  }

  //When this feature flag is enabled, we allow any model to be selected -- so this check is against available models.
  if (allowOtherModels) {
    const availableModelNames = new Set();

    availableModels.forEach((model) => {
      availableModelNames.add(model.modelName);
      if (model.children) {
        model.children.forEach((child) => {
          availableModelNames.add(child.modelName);
        });
      }
    });

    return availableModelNames.has(model);
  }

  //this is the code path for when the feature flag is disabled -- we check against the effective prioritized list (whitelist or defaults).
  const effectivePrioritizedModels =
    getEffectivePrioritizedModels(loopAllowedModels);
  for (const modelName of effectivePrioritizedModels) {
    if (model.includes(modelName)) {
      if (
        availableModels.some(
          (availableModel) => availableModel.modelName === model,
        )
      ) {
        return true;
      }
      if (
        availableModels.some((availableModel) =>
          availableModel.children?.some((child) => child.modelName === model),
        )
      ) {
        return true;
      }
    }
  }
  return false;
};

// Normalize allowed models into a Map<provider, Set<modelName>> for quick lookups
export function normalizeLoopAllowedModels(
  loopAllowedModels: LoopAllowedModels,
): Map<string, Set<string>> {
  const map = new Map<string, Set<string>>();
  if (!Array.isArray(loopAllowedModels)) return map;
  for (const { provider, modelName } of loopAllowedModels) {
    let set = map.get(provider);
    if (!set) {
      set = new Set<string>();
      map.set(provider, set);
    }
    set.add(modelName);
  }
  return map;
}

// Lookup max_output_tokens for a model by name (searches children as well)
export function modelMaxOutputTokens(
  model: string,
  availableModels: ModelItem[],
): number | undefined {
  const byName = indexModelsByName(availableModels);
  const details = byName.get(model);
  return details?.max_output_tokens ?? undefined;
}

export function deriveDefaultModel(
  availableModels: ModelItem[],
  loopAllowedModels: LoopAllowedModels = null,
): string {
  if (!availableModels || availableModels.length === 0) return "";

  const effectivePrioritizedModels =
    getEffectivePrioritizedModels(loopAllowedModels);

  for (const prioritized of effectivePrioritizedModels) {
    for (const modelDetails of availableModels) {
      if (
        isModelMatch(modelDetails.modelName, prioritized, modelDetails.provider)
      ) {
        return modelDetails.modelName;
      }
      if (modelDetails.children) {
        for (const child of modelDetails.children) {
          if (
            isModelMatch(child.modelName, prioritized, modelDetails.provider)
          ) {
            return child.modelName;
          }
        }
      }
    }
  }
  return "";
}

// Index models by name including children for faster metadata lookups
export function indexModelsByName(
  availableModels: ModelDetails[],
): Map<string, ModelDetails> {
  const map = new Map<string, ModelDetails>();
  for (const m of availableModels) {
    map.set(m.modelName, m);
    if (m.children) {
      for (const c of m.children) map.set(c.modelName, c);
    }
  }
  return map;
}

export function getDefaultWhitelistedModels(
  availableModels: ModelItem[],
): ModelItem[] {
  return availableModels.filter((m) =>
    PRIORITIZED_DEFAULT_MODELS.some((defaultModel) =>
      isModelMatch(m.modelName, defaultModel, m.provider),
    ),
  );
}
