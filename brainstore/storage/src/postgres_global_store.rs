use async_trait::async_trait;
use futures::{
    future::join_all,
    join, pin_mut,
    stream::{StreamExt, TryStreamExt},
};
use lazy_static::lazy_static;
use otel_common::opentelemetry::{
    global,
    metrics::{Counter, Gauge, Histogram},
    KeyValue,
};
use rust_decimal::prelude::ToPrimitive;
use rust_decimal::Decimal;
use std::{
    collections::{HashMap, HashSet},
    fmt::Write,
    str::FromStr,
    sync::{Arc, Mutex},
};
use tokio_postgres::types::ToSql;
use tracing::{instrument, Instrument};
use util::{
    anyhow::{anyhow, Result},
    chrono::{DateTime, Utc},
    system_types::{FullObjectId, FullObjectIdOwned, FullRowId, FullRowIdOwned, ObjectType},
    url::Url,
    uuid::Uuid,
    xact::{PaginationKey, TransactionId},
};

use crate::{
    global_store::{
        BackfillBrainstoreObject, BackfillTrackingEntry, BackfillTrackingEntryId,
        BackfillTrackingEntryUpdate, GlobalStore, IdSegmentMembershipType, LastCompactedIndexMeta,
        LastIndexOpTokenOpts, LastIndexOperation, LastIndexOperationResult,
        ListSegmentIdsGlobalOptionalInput, ListSegmentIdsOptionalInput, ObjectMetadata,
        ObjectMetadataUpdate, QueryUncompactedSegmentsCursor, QueryUncompactedSegmentsItem,
        RecentObject, RecentObjectCursor, SegmentDerivedStatistics, SegmentFieldStatistics,
        SegmentLiveness, SegmentMetadata, SegmentMetadataUpdate, SegmentVacuumState,
        SegmentWalEntriesCursor, SegmentWalEntriesXactIdStatistic, SegmentWalEntry, TaskInfo,
        TaskInfos, TimeBasedRetentionCursor, TimeBasedRetentionInfo, TimeBasedRetentionOperation,
        TimeBasedRetentionState, UpsertSegmentWalEntry, VacuumIndexInfo, VacuumSegmentWalInfo,
    },
    healthcheck_util::validate_postgres_connection,
    instrumented::Instrumented,
    limits::global_limits,
    postgres_pool::PostgresPool,
    postgres_query_util::{
        make_brainstore_object_id, make_object_id_column_expr, PostgresObjectIdFields,
    },
    tantivy_index::IndexMetaJson,
    time,
    timer::TimerManager,
    vacuum::VacuumType,
};

pub(crate) const SEGMENT_ID_TO_LIVENESS_TABLE: &str =
    "brainstore_global_store_segment_id_to_liveness";
const OBJECT_ID_TO_METADATA_TABLE: &str = "brainstore_global_store_object_id_to_metadata";
const SEGMENT_ID_TO_METADATA_TABLE: &str = "brainstore_global_store_segment_id_to_metadata";
const LEGACY_SEGMENT_ID_TO_ROW_INFO_TABLE: &str = "brainstore_global_store_segment_id_to_row_info";
const ROW_ID_TO_SEGMENT_ID_TABLE: &str = "brainstore_global_store_row_id_to_segment_id";
const ROOT_SPAN_ID_TO_SEGMENT_ID_TABLE: &str = "brainstore_global_store_root_span_id_to_segment_id";
const SEGMENT_ID_TO_WAL_ENTRIES_TABLE: &str = "brainstore_global_store_segment_id_to_wal_entries";
const SEGMENT_ID_TO_LAST_INDEX_OPERATION_TABLE: &str =
    "brainstore_global_store_segment_id_to_last_index_operation";
const TIME_BASED_RETENTION_STATE_TABLE: &str = "brainstore_global_store_time_based_retention_state";
pub(crate) const SEGMENT_ID_TO_TASK_INFO_TABLE: &str =
    "brainstore_global_store_segment_id_to_task_info";
const BACKFILL_TRACKING_ENTRIES_TABLE: &str = "brainstore_backfill_tracked_objects";
const LOGS_TABLE: &str = "logs";
const LOGS2_TABLE: &str = "logs2";

#[derive(Debug, Clone)]
pub struct PostgresGlobalStore {
    pool: PostgresPool,
    debug_timer: Arc<TimerManager>,
}

lazy_static::lazy_static! {
    static ref GLOBAL_STORE_POOLS: Mutex<HashMap<Url, PostgresPool>> = Mutex::new(HashMap::new());
}

impl PostgresGlobalStore {
    pub fn new(url: &Url) -> Result<Self> {
        let pool = GLOBAL_STORE_POOLS
            .lock()
            .unwrap_or_else(|e| {
                // If the mutex is poisoned, recover by creating a new lock guard
                eprintln!("Warning: GLOBAL_STORE_POOLS mutex was poisoned, recovering...");
                e.into_inner()
            })
            .entry(url.clone())
            .or_insert_with(|| {
                PostgresPool::new(url, global_limits().global_store_pool_size).unwrap()
            })
            .clone();
        Ok(Self {
            pool,
            debug_timer: TimerManager::new("PostgresGlobalStore"),
        })
    }

    #[instrument(err, level = "debug", skip(self))]
    async fn get_connection(&self) -> Result<deadpool_postgres::Object> {
        Ok(self.pool.get_client().await?)
    }

    #[instrument(
        err,
        skip(self),
        name = "PostgresGlobalStore::refresh_segment_id_earliest_uncompacted_xact_id"
    )]
    async fn refresh_segment_id_earliest_uncompacted_xact_id(
        &self,
        segment_ids: &[Uuid],
    ) -> Result<()> {
        if segment_ids.is_empty() {
            return Ok(());
        }

        // Use the same LATERAL JOIN strategy as we do in
        // query_segment_wal_entries_xact_id_statistic.
        //
        //        QUERY PLAN
        //---------------------------------------------------------------------------------------------
        // Update on brainstore_global_store_segment_id_to_liveness  (cost=1.12..58.45 rows=0 width=0)
        //   CTE segment_ids
        //     ->  Function Scan on unnest s_1  (cost=0.00..0.10 rows=10 width=16)
        //   ->  Nested Loop  (cost=1.02..58.35 rows=10 width=126)
        //         ->  Hash Right Join  (cost=0.74..35.32 rows=10 width=136)
        //               Hash Cond: (s.segment_id = segment_ids.segment_id)
        //               ->  Nested Loop  (cost=0.41..34.86 rows=10 width=96)
        //                     ->  CTE Scan on segment_ids s  (cost=0.00..0.20 rows=10 width=56)
        //                     ->  Subquery Scan on e  (cost=0.41..3.46 rows=1 width=40)
        //                           ->  Limit  (cost=0.41..3.45 rows=1 width=8)
        //                                 ->  Index Scan using brainstore_global_store_segme_segment_id_xact_id_wal_filena_idx on brainstore_global_store_segment_id_to_wal_entries  (cost=0.41..3.45 rows=1 width=8)
        //                                       Index Cond: (segment_id = s.segment_id)
        //                                       Filter: (deleted_at IS NULL)
        //               ->  Hash  (cost=0.20..0.20 rows=10 width=56)
        //                     ->  CTE Scan on segment_ids  (cost=0.00..0.20 rows=10 width=56)
        //         ->  Index Scan using brainstore_global_store_segment_id_to_liveness_pkey on brainstore_global_store_segment_id_to_liveness  (cost=0.29..2.30 rows=1 width=22)
        //               Index Cond: (segment_id = segment_ids.segment_id)
        let query = format!(
            r#"
            with
            segment_ids as (
                select segment_id from unnest($1::uuid[]) s(segment_id)
            ),
            earliest_uncompacted_xact_ids as (
                select s.segment_id, e.xact_id
                from segment_ids s
                cross join lateral (
                    select xact_id
                    from {}
                    where segment_id = s.segment_id and deleted_at is null and not is_compacted
                    order by xact_id asc
                    limit 1
                ) e
            ),
            segment_id_to_xact_id as (
                select segment_id, xact_id
                from segment_ids left join earliest_uncompacted_xact_ids using (segment_id)
            )
            update {}
            set derived_earliest_uncompacted_xact_id = e.xact_id
            from segment_id_to_xact_id e
            where {}.segment_id = e.segment_id
            "#,
            SEGMENT_ID_TO_WAL_ENTRIES_TABLE,
            SEGMENT_ID_TO_LIVENESS_TABLE,
            SEGMENT_ID_TO_LIVENESS_TABLE,
        );
        let _ = self
            .get_connection()
            .await?
            .execute_raw(query.as_str(), &[&segment_ids])
            .await?;
        Ok(())
    }
}

#[async_trait]
impl GlobalStore for PostgresGlobalStore {
    #[instrument(err, skip(self), name = "PostgresGlobalStore::list_segment_ids_global")]
    async fn list_segment_ids_global(
        &self,
        optional_input: Option<ListSegmentIdsGlobalOptionalInput>,
    ) -> Result<Vec<Uuid>> {
        time!(self.debug_timer, "list_segment_ids_global");

        let optional_input = optional_input.unwrap_or_default();

        let mut params: Vec<&(dyn ToSql + Sync)> = Vec::new();

        // These variables just hold memory for the lifetime of the function.
        let mut _cursor: Uuid = Uuid::nil();
        let mut _limit: i64 = 0;
        let pagination_clause = match optional_input.pagination_args {
            None => "".to_string(),
            Some(args) => {
                let cursor_filter = match args.segment_id_cursor {
                    Some(cursor) => {
                        _cursor = cursor;
                        params.push(&_cursor);
                        "and segment_id > $1::uuid"
                    }
                    None => "",
                };
                _limit = args.limit as i64;
                params.push(&_limit);
                format!(
                    "{} order by segment_id asc limit ${}",
                    cursor_filter,
                    params.len()
                )
            }
        };

        let is_live_clause = if optional_input.is_live {
            "is_live"
        } else {
            "not is_live"
        };

        // postgres=# explain select segment_id from brainstore_global_store_segment_id_to_liveness where is_live and segment_id > '2d093362-fce9-48af-92ad-b030c2052b9b' order by segment_id asc limit 10;
        //                                                                                        QUERY PLAN
        // ----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
        //  Limit  (cost=0.29..0.77 rows=10 width=16)
        //    ->  Index Only Scan using brainstore_global_store_segment_id_to_li_is_live_segment_id_idx on brainstore_global_store_segment_id_to_liveness  (cost=0.29..723.32 rows=14842 width=16)
        //          Index Cond: ((is_live = true) AND (segment_id > '2d093362-fce9-48af-92ad-b030c2052b9b'::uuid))

        let rows = self
            .get_connection()
            .await?
            .query_raw(
                format!(
                    "select segment_id from {} where {} {}",
                    SEGMENT_ID_TO_LIVENESS_TABLE, is_live_clause, pagination_clause,
                )
                .as_str(),
                params,
            )
            .await?;

        rows.map(|row| Ok(row?.get::<usize, Uuid>(0)))
            .try_collect()
            .await
    }

    #[instrument(err, skip(self), name = "PostgresGlobalStore::list_segment_ids")]
    async fn list_segment_ids(
        &self,
        object_ids: &[FullObjectId],
        optional_input: Option<ListSegmentIdsOptionalInput>,
    ) -> Result<Vec<Vec<Uuid>>> {
        time!(self.debug_timer, "list_segment_ids");
        if object_ids.is_empty() {
            return Ok(Vec::new());
        }

        let object_id_strs = object_ids.iter().map(|o| o.to_string()).collect::<Vec<_>>();
        let object_id_to_idx = object_id_strs
            .iter()
            .enumerate()
            .map(|(idx, object_id)| (object_id.as_str(), idx))
            .collect::<HashMap<&str, usize>>();
        let mut out: Vec<Vec<Uuid>> = vec![Vec::new(); object_ids.len()];

        let is_live_clause = if optional_input.unwrap_or_default().is_live {
            "is_live"
        } else {
            "not is_live"
        };

        let rows = self
            .get_connection()
            .await?
            .query_raw(
                format!(
                    "select object_id, segment_id from {} where object_id = any($1::text[]) and {}",
                    SEGMENT_ID_TO_LIVENESS_TABLE, is_live_clause,
                )
                .as_str(),
                &[&object_id_strs],
            )
            .await?;
        pin_mut!(rows);
        while let Some(row) = rows.next().await {
            let row = row?;
            let object_id: &str = row.get(0);
            let segment_id: Uuid = row.get(1);
            let idx = object_id_to_idx[object_id];
            out[idx].push(segment_id);
        }

        Ok(out)
    }

    #[instrument(err, skip(self), name = "PostgresGlobalStore::query_segment_liveness")]
    async fn query_segment_liveness(&self, segment_ids: &[Uuid]) -> Result<Vec<SegmentLiveness>> {
        time!(self.debug_timer, "query_segment_liveness");
        if segment_ids.is_empty() {
            return Ok(Vec::new());
        }

        let query = format!(
            "select segment_id, object_id, is_live from {} where segment_id = any($1::uuid[])",
            SEGMENT_ID_TO_LIVENESS_TABLE,
        );
        let rows = self
            .get_connection()
            .await?
            .query_raw(query.as_str(), &[&segment_ids])
            .await?;
        let mut segment_id_to_object_id: HashMap<_, _> = rows
            .map(|row| -> Result<(Uuid, SegmentLiveness)> {
                let row = row?;
                let segment_id: Uuid = row.get(0);
                let object_id: String = row.get(1);
                let object_id = FullObjectIdOwned::from_str(&object_id)?;
                let is_live: bool = row.get(2);
                Ok((segment_id, SegmentLiveness { object_id, is_live }))
            })
            .try_collect()
            .await?;
        segment_ids
            .iter()
            .map(|segment_id| {
                segment_id_to_object_id
                    .remove(segment_id)
                    .ok_or_else(|| anyhow!("missing object_id for segment_id {}", segment_id))
            })
            .collect()
    }

    #[instrument(
        err,
        skip(self, object_id_add_remove_segment_ids),
        name = "PostgresGlobalStore::update_segment_ids"
    )]
    async fn update_segment_ids(
        &self,
        object_id_add_remove_segment_ids: &[(FullObjectId, &[Uuid], &[Uuid])],
    ) -> Result<()> {
        time!(self.debug_timer, "update_segment_ids");
        let object_id_strs: Vec<String> = object_id_add_remove_segment_ids
            .iter()
            .map(|(object_id, _, _)| object_id.to_string())
            .collect();
        let mut add_ids: Vec<&(dyn ToSql + Sync)> = Vec::new();
        let mut remove_ids: Vec<&(dyn ToSql + Sync)> = Vec::new();
        for (object_id, (_, add_segment_ids, remove_segment_ids)) in object_id_strs
            .iter()
            .zip(object_id_add_remove_segment_ids.iter())
        {
            for add_segment_id in add_segment_ids.iter() {
                add_ids.push(add_segment_id);
                add_ids.push(object_id);
            }
            for remove_segment_id in remove_segment_ids.iter() {
                remove_ids.push(remove_segment_id);
                remove_ids.push(object_id);
            }
        }

        if add_ids.is_empty() && remove_ids.is_empty() {
            return Ok(());
        }

        let has_add_ids = !add_ids.is_empty();
        {
            let mut conn = self.get_connection().await?;
            let transaction = conn.transaction().await?;

            if !remove_ids.is_empty() {
                let num_segments = remove_ids.len() / 2;
                let remove_query = format!(
                  "update {} set is_live = false where (segment_id, object_id) in ({}) and is_live",
                  SEGMENT_ID_TO_LIVENESS_TABLE,
                  (1..1 + num_segments)
                      .map(|i| format!("(${}::uuid, ${}::text)", i * 2 - 1, i * 2))
                      .collect::<Vec<_>>()
                      .join(", ")
              );
                let num_removed_rows = transaction
                    .execute_raw(remove_query.as_str(), remove_ids.into_iter())
                    .await?;
                if num_removed_rows != num_segments as u64 {
                    return Err(anyhow!("failed to remove all segment_ids"));
                }
            }
            if has_add_ids {
                let num_segments = add_ids.len() / 2;
                let add_query = format!(
                    "insert into {}(segment_id, object_id, is_live) values {}",
                    SEGMENT_ID_TO_LIVENESS_TABLE,
                    (1..1 + num_segments)
                        .map(|i| format!("(${}::uuid, ${}::text, true)", i * 2 - 1, i * 2))
                        .collect::<Vec<_>>()
                        .join(", ")
                );
                let num_inserted_rows = transaction
                    .execute_raw(add_query.as_str(), add_ids.into_iter())
                    .await?;
                if num_inserted_rows != num_segments as u64 {
                    return Err(anyhow!("failed to add all segment_ids"));
                }
            }
            transaction.commit().await?;
        }

        if has_add_ids {
            let added_segment_ids: Vec<Uuid> = object_id_add_remove_segment_ids
                .iter()
                .flat_map(|(_, add_segment_ids, _)| *add_segment_ids)
                .cloned()
                .collect();
            self.refresh_segment_id_earliest_uncompacted_xact_id(&added_segment_ids)
                .await?;
        }
        Ok(())
    }

    #[instrument(
        err,
        skip(self, segment_ids),
        name = "PostgresGlobalStore::purge_segment_ids"
    )]
    async fn purge_segment_ids(&self, segment_ids: &[Uuid]) -> Result<()> {
        time!(self.debug_timer, "purge_segment_ids");
        if segment_ids.is_empty() {
            return Ok(());
        }

        let conn = self.get_connection().await?;
        let check_query = format!(
            "select exists(select 1 from {} where segment_id = any($1::uuid[]) and is_live)",
            SEGMENT_ID_TO_LIVENESS_TABLE,
        );
        let has_live_segments: bool = {
            let rows = conn
                .query_raw(check_query.as_str(), &[&segment_ids])
                .await?;
            pin_mut!(rows);
            rows.next()
                .await
                .ok_or_else(|| anyhow!("missing row"))??
                .get(0)
        };
        if has_live_segments {
            return Err(anyhow!("Cannot purge live segments"));
        }

        let delete_query = format!(
            "delete from {} where segment_id = any($1::uuid[])",
            SEGMENT_ID_TO_LIVENESS_TABLE,
        );
        conn.execute_raw(delete_query.as_str(), &[&segment_ids])
            .await?;

        Ok(())
    }

    #[instrument(err, skip(self), name = "PostgresGlobalStore::query_segment_metadatas")]
    async fn query_segment_metadatas(&self, segment_ids: &[Uuid]) -> Result<Vec<SegmentMetadata>> {
        time!(self.debug_timer, "query_segment_metadatas");
        if segment_ids.is_empty() {
            return Ok(Vec::new());
        }

        let query = format!(
            "select segment_id, last_compacted_index_meta_xact_id, last_compacted_index_meta_tantivy_meta, minimum_pagination_key, num_rows from {} where segment_id = any($1::uuid[])",
            SEGMENT_ID_TO_METADATA_TABLE,
        );
        let rows = self
            .get_connection()
            .await?
            .query_raw(query.as_str(), &[&segment_ids])
            .await?;
        let mut segment_id_to_metadata: HashMap<_, _> = rows
            .map(|row| get_segment_id_and_metadata(&row?))
            .try_collect()
            .await?;
        segment_ids
            .iter()
            .map(|segment_id| {
                segment_id_to_metadata
                    .remove(segment_id)
                    .ok_or_else(|| anyhow!("missing object_id for segment_id {}", segment_id))
            })
            .collect()
    }

    #[instrument(
        err,
        skip(self),
        name = "PostgresGlobalStore::query_uncompacted_segments"
    )]
    async fn query_uncompacted_segments(
        &self,
        limit: i64,
        cursor: Option<QueryUncompactedSegmentsCursor>,
    ) -> Result<Vec<QueryUncompactedSegmentsItem>> {
        time!(self.debug_timer, "query_uncompacted_segments");

        //                                                                                                QUERY PLAN
        // -----------------------------------------------------------------------------------------------------------------------------------------------------------------------
        //  Limit  (cost=0.41..89.87 rows=100 width=862)
        //    ->  Nested Loop  (cost=0.41..3605.58 rows=4030 width=862)
        //          ->  Index Scan using brainstore_global_store_segme_derived_earliest_uncompacted__idx on brainstore_global_store_segment_id_to_liveness l  (cost=0.12..532.43 rows=4030 width=71)
        //                Index Cond: (ROW(derived_earliest_uncompacted_xact_id, segment_id) < ROW('1000194405700212341'::bigint, 'ffe0938d-0997-44d4-98fe-eaa73b8175e5'::uuid))
        //          ->  Index Scan using brainstore_global_store_segment_id_to_metadata_pkey on brainstore_global_store_segment_id_to_metadata m  (cost=0.29..0.76 rows=1 width=807)
        //                Index Cond: (segment_id = l.segment_id)

        // Build the query joining liveness with derived statistics and metadata.
        let mut query = format!(
            "SELECT l.segment_id, m.last_compacted_index_meta_xact_id, m.last_compacted_index_meta_tantivy_meta,
                    m.minimum_pagination_key, m.num_rows, l.object_id, l.derived_earliest_uncompacted_xact_id
             FROM {} l
             JOIN {} m ON l.segment_id = m.segment_id
             WHERE l.is_live = true AND l.derived_earliest_uncompacted_xact_id IS NOT NULL",
            SEGMENT_ID_TO_LIVENESS_TABLE,
            SEGMENT_ID_TO_METADATA_TABLE
        );

        let mut params: Vec<&(dyn ToSql + Sync)> = Vec::new();
        let mut _cursor_xact_id_i64: i64 = 0;
        let mut _cursor_segment_id: Option<Uuid> = None;

        // Apply cursor filter if provided
        if let Some(ref cursor) = cursor {
            _cursor_xact_id_i64 = cursor.earliest_uncompacted_xact_id.0 as i64;
            _cursor_segment_id = cursor.segment_id;

            params.push(&_cursor_xact_id_i64);
            if let Some(ref seg_id) = _cursor_segment_id {
                params.push(seg_id);
                query.push_str(&format!(
                    " AND (l.derived_earliest_uncompacted_xact_id, l.segment_id) < (${}::bigint, ${}::uuid)",
                    params.len() - 1, params.len()
                ));
            } else {
                query.push_str(&format!(
                    " AND l.derived_earliest_uncompacted_xact_id < ${}::bigint",
                    params.len()
                ));
            }
        }

        // Order by (earliest_uncompacted_xact_id DESC, segment_id DESC) and apply limit
        params.push(&limit);
        query.push_str(&format!(
            " ORDER BY l.derived_earliest_uncompacted_xact_id DESC, l.segment_id DESC LIMIT ${}::bigint",
            params.len()
        ));

        let rows = self.get_connection().await?.query(&query, &params).await?;

        let mut results = Vec::new();
        for row in rows {
            let (segment_id, metadata) = get_segment_id_and_metadata(&row)?;
            let object_id_str: String = row.get(5);
            let object_id: FullObjectIdOwned = object_id_str.parse()?;
            let earliest_uncompacted_xact_id = get_opt_xact_id(&row, 6).ok_or_else(|| {
                anyhow!("Expected derived_earliest_uncompacted_xact_id to be present")
            })?;
            results.push(QueryUncompactedSegmentsItem {
                object_id,
                segment_metadata: metadata,
                segment_id,
                earliest_uncompacted_xact_id,
            });
        }

        Ok(results)
    }

    #[instrument(
        err,
        skip(self),
        fields(num_segment_ids = segment_ids.len()),
        name = "PostgresGlobalStore::query_segment_id_derived_statistics"
    )]
    async fn query_segment_id_derived_statistics(
        &self,
        segment_ids: &[Uuid],
    ) -> Result<Vec<SegmentDerivedStatistics>> {
        time!(self.debug_timer, "query_segment_id_derived_statistics");

        if segment_ids.is_empty() {
            return Ok(Vec::new());
        }

        // QUERY PLAN
        //
        // ------------------------------------------------------------------------------------------------------------------------------------------------------------
        //  Index Scan using brainstore_global_store_segment_id_to_liveness_pkey on brainstore_global_store_segment_id_to_liveness  (cost=0.29..23.02 rows=10 width=24)
        //    Index Cond: (segment_id = ANY ('{66fad460-0256-4878-9e92-309b14625b9f,dcb8dd80-9066-4298-94a3-1514af447791,ae08edf3-0fde-4776-b7c6-7f9108c0e881,1ed1e915-be0b-4780-98f5-5f133db31944,121ec169-40b6-4f36-a9e4-d3e88bd11d56,774de0ab-abea-4cba-9f7b-5fad681acdf8,a01c6374-fe81-4ae5-a0d7-80a199b2c6a8,354e16ca-4e6a-4cd8-9f87-fc2a20901497,0c353695-218c-4a91-9638-90ee7c7d954c,baa99abd-6761-40ae-bcc7-67a1579aeb02}'::uuid[]))
        //
        let query = format!(
            "SELECT segment_id, derived_earliest_uncompacted_xact_id FROM {} WHERE segment_id = ANY($1::uuid[])",
            SEGMENT_ID_TO_LIVENESS_TABLE
        );

        let rows = self
            .get_connection()
            .await?
            .query(&query, &[&segment_ids])
            .await?;

        let mut segment_id_to_stats: HashMap<Uuid, SegmentDerivedStatistics> = HashMap::new();
        for row in rows {
            let segment_id: Uuid = row.get(0);
            let earliest_uncompacted_xact_id = get_opt_xact_id(&row, 1);
            segment_id_to_stats.insert(
                segment_id,
                SegmentDerivedStatistics {
                    earliest_uncompacted_xact_id,
                },
            );
        }

        // Return statistics in the same order as input segment_ids
        segment_ids
            .iter()
            .map(|segment_id| {
                segment_id_to_stats
                    .get(segment_id)
                    .ok_or_else(|| {
                        anyhow!("missing derived statistics for segment_id {}", segment_id)
                    })
                    .cloned()
            })
            .collect()
    }

    #[instrument(
        err,
        skip(self),
        fields(num_segment_ids = segment_ids.len()),
        name = "PostgresGlobalStore::refresh_segment_id_derived_statistics"
    )]
    async fn refresh_segment_id_derived_statistics(&self, segment_ids: &[Uuid]) -> Result<()> {
        if segment_ids.is_empty() {
            return Ok(());
        }
        self.refresh_segment_id_earliest_uncompacted_xact_id(segment_ids)
            .await?;
        Ok(())
    }

    #[instrument(
        err,
        skip(self, updates),
        name = "PostgresGlobalStore::upsert_segment_metadatas"
    )]
    async fn upsert_segment_metadatas(
        &self,
        updates: HashMap<Uuid, SegmentMetadataUpdate>,
    ) -> Result<bool> {
        time!(self.debug_timer, "upsert_segment_metadatas");
        if updates.is_empty() {
            return Ok(true);
        }

        let segment_ids = updates.keys().cloned().collect::<Vec<_>>();

        let mut conn = self.get_connection().await?;
        let mut transaction = conn.transaction().await?;
        let applied = self
            .upsert_segment_metadatas_within_tx(updates, &mut transaction)
            .await?;
        if !applied {
            return Ok(false);
        }

        self.upsert_segment_last_written_ts_within_tx(&segment_ids, None, &mut transaction)
            .await?;
        transaction.commit().await?;
        Ok(true)
    }

    #[instrument(
        err,
        skip(self, segment_ids),
        name = "PostgresGlobalStore::purge_segment_metadatas"
    )]
    async fn purge_segment_metadatas(&self, segment_ids: &[Uuid]) -> Result<()> {
        time!(self.debug_timer, "purge_segment_metadatas");
        if segment_ids.is_empty() {
            return Ok(());
        }

        let conn = self.get_connection().await?;
        let check_query = format!(
            "select exists(select 1 from {} where segment_id = any($1::uuid[]) and is_live)",
            SEGMENT_ID_TO_LIVENESS_TABLE,
        );
        let has_live_segments: bool = {
            let rows = conn
                .query_raw(check_query.as_str(), &[&segment_ids])
                .await?;
            pin_mut!(rows);
            rows.next()
                .await
                .ok_or_else(|| anyhow!("missing row"))??
                .get(0)
        };
        if has_live_segments {
            return Err(anyhow!("Cannot purge live segments"));
        }

        let delete_query = format!(
            "delete from {} where segment_id = any($1::uuid[])",
            SEGMENT_ID_TO_METADATA_TABLE,
        );
        conn.execute_raw(delete_query.as_str(), &[&segment_ids])
            .await?;

        Ok(())
    }

    #[instrument(err, skip(self), name = "PostgresGlobalStore::query_object_metadatas")]
    async fn query_object_metadatas(
        &self,
        object_ids: &[FullObjectId],
    ) -> Result<Vec<ObjectMetadata>> {
        time!(self.debug_timer, "query_object_metadatas");
        if object_ids.is_empty() {
            return Ok(Vec::new());
        }

        let object_id_strs = object_ids.iter().map(|o| o.to_string()).collect::<Vec<_>>();
        let object_id_to_idx = object_id_strs
            .iter()
            .enumerate()
            .map(|(idx, object_id)| (object_id.as_str(), idx))
            .collect::<HashMap<&str, usize>>();
        let mut out: Vec<Option<ObjectMetadata>> = vec![None; object_ids.len()];

        // Since most of the time objects should exist, we first query for all objects and then
        // fill in any missing ones.
        //
        // NOTE: if adjusting this query, make sure to adjust the analogous one in
        // api-ts/src/brainstore/wal.ts.
        let rows = self
            .get_connection()
            .await?
            .query_raw(
                format!(
                    r#"
                        with
                        all_object_ids as (
                            select unnest($1::text[]) as object_id
                        ),
                        existing_objects as (
                            select object_id, last_processed_xact_id, wal_token
                            from {} join all_object_ids using (object_id)
                        ),
                        new_objects as (
                            insert into {}(object_id)
                            select object_id from all_object_ids
                            where object_id not in (select object_id from existing_objects)
                            -- This cannot be an 'on conflict do nothing' because of race
                            -- conditions with a concurrent delete. Even if the row ends up deleted at
                            -- the end of the day, we need to return something.
                            on conflict (object_id) do update set object_id = excluded.object_id
                            returning object_id, last_processed_xact_id, wal_token
                        )
                        select * from existing_objects
                        union all select * from new_objects
                    "#,
                    OBJECT_ID_TO_METADATA_TABLE,
                    OBJECT_ID_TO_METADATA_TABLE,
                )
                .as_str(),
                &[&object_id_strs],
            )
            .await?;
        pin_mut!(rows);
        while let Some(row) = rows.next().await {
            let row = row?;
            let object_id: &str = row.get(0);
            let last_processed_xact_id = get_opt_xact_id(&row, 1);
            let wal_token: Uuid = row.get(2);
            let idx = object_id_to_idx[object_id];
            assert!(out[idx].is_none());
            out[idx] = Some(ObjectMetadata {
                last_processed_xact_id,
                wal_token,
            });
        }
        Ok(out.into_iter().map(|x| x.unwrap()).collect())
    }

    #[instrument(
        err,
        skip(self),
        name = "PostgresGlobalStore::query_recently_updated_objects"
    )]
    async fn query_recently_updated_objects<'a>(
        &self,
        cursor: Option<RecentObjectCursor<'a>>,
        limit: usize,
        min_lag_compaction_seconds: Option<u64>,
    ) -> Result<Vec<RecentObject>> {
        time!(self.debug_timer, "query_recently_updated_objects");

        let mut params: Vec<&(dyn ToSql + Sync)> = Vec::new();
        // These variables just hold memory for the lifetime of the function.
        let mut _cursor_xact_id_i64 = 0;
        let mut _cursor_object_id_str = String::new();
        let mut _min_lag_seconds_i64 = 0;

        let where_clause = if let Some(cursor) = cursor {
            _cursor_xact_id_i64 = cursor.last_processed_xact_id.0 as i64;
            params.push(&_cursor_xact_id_i64);

            if let Some(object_id) = cursor.object_id {
                // If the cursor has an object ID, we use it to filter the results.
                _cursor_object_id_str = object_id.to_string();
                params.push(&_cursor_object_id_str);
                "(last_processed_xact_id, object_id) < ($1, $2)"
            } else {
                "(last_processed_xact_id) < $1"
            }
        } else {
            "true"
        };

        // Build the compaction lag filter
        let (cte_query, lag_filter) = if let Some(min_lag_seconds) = min_lag_compaction_seconds {
            _min_lag_seconds_i64 = min_lag_seconds as i64;
            params.push(&_min_lag_seconds_i64);

            (
                format!(
                    r#"WITH last_compacted AS (
    SELECT
        l.object_id,
        MAX(m.last_compacted_index_meta_xact_id) AS last_compacted_xact_id
    FROM   {} l
    JOIN   {} m USING (segment_id)
    WHERE  l.is_live
    GROUP  BY l.object_id
),
objects_with_lag AS (
    SELECT
        g.object_id
    FROM        {} g
    LEFT JOIN   last_compacted c USING (object_id)
    WHERE
    (c.last_compacted_xact_id IS NULL AND (EXTRACT(EPOCH FROM NOW())::bigint - ((g.last_processed_xact_id >> 16) & X'FFFFFFFF'::bigint)) > ${})
    OR (((g.last_processed_xact_id >> 16) & X'FFFFFFFF'::bigint) - ((c.last_compacted_xact_id >> 16) & X'FFFFFFFF'::bigint)) > ${}
)"#,
                    SEGMENT_ID_TO_LIVENESS_TABLE,
                    SEGMENT_ID_TO_METADATA_TABLE,
                    OBJECT_ID_TO_METADATA_TABLE,
                    params.len(),
                    params.len(),
                ),
                "AND object_id IN (SELECT object_id FROM objects_with_lag)".to_string(),
            )
        } else {
            ("".to_string(), "".to_string())
        };

        let rows = self
            .get_connection()
            .await?
            .query_raw(
                format!(
                    r#"
{}
                        select object_id, last_processed_xact_id
                        from {}
                        where {}
                        AND last_processed_xact_id IS NOT NULL
                        {}
                        order by last_processed_xact_id DESC, object_id DESC
                        limit {}
                    "#,
                    cte_query, OBJECT_ID_TO_METADATA_TABLE, where_clause, lag_filter, limit,
                )
                .as_str(),
                params.into_iter(),
            )
            .await?;
        pin_mut!(rows);
        let mut out = Vec::new();
        while let Some(row) = rows.next().await {
            let row = row?;
            let object_id: &str = row.get(0);
            let last_processed_xact_id = get_opt_xact_id(&row, 1)
                .ok_or_else(|| anyhow!("missing last_processed_xact_id"))?;
            out.push(RecentObject {
                last_processed_xact_id,
                object_id: object_id.parse()?,
            });
        }

        Ok(out)
    }

    #[instrument(err, skip(self), name = "PostgresGlobalStore::upsert_object_metadatas")]
    async fn upsert_object_metadatas(
        &self,
        updates: HashMap<FullObjectId<'_>, ObjectMetadataUpdate>,
    ) -> Result<bool> {
        time!(self.debug_timer, "upsert_object_metadatas");
        if updates.is_empty() {
            return Ok(true);
        }
        let updates = updates
            .into_iter()
            .map(|(object_id, update)| (object_id.to_string(), update))
            .collect::<HashMap<_, _>>();

        let mut conn = self.get_connection().await?;
        let transaction = conn.transaction().await?;

        // First grab all existing rows matching any of the given object IDs, grabbing a write lock
        // on any matching rows.
        let update_object_ids = updates.keys().collect::<Vec<_>>();
        let rows = transaction
            .query_raw(
                &format!(
                    r#"
                    select object_id, last_processed_xact_id
                    from {} where object_id = any($1::text[])
                    for update
                "#,
                    OBJECT_ID_TO_METADATA_TABLE,
                ),
                &[&update_object_ids],
            )
            .await?;
        let object_id_to_existing_metadata: HashMap<String, ObjectMetadata> = rows
            .map(|row| -> Result<(String, ObjectMetadata)> { get_object_id_and_metadata(&row?) })
            .try_collect()
            .await?;

        // Next, do the CAS checks between any existing metadatas and the updates.
        for (object_id, update) in &updates {
            if let Some(existing_metadata) = object_id_to_existing_metadata.get(object_id.as_str())
            {
                if let Some((prev_xact_id, _)) = &update.last_processed_xact_id {
                    if existing_metadata.last_processed_xact_id != *prev_xact_id {
                        return Ok(false);
                    }
                }
            }
        }

        // Prepare a list of updated row values for an upsert operation.
        let updated_metadatas = {
            let mut out: HashMap<String, PostgresObjectMetadata> = HashMap::new();
            for (object_id, update) in updates.iter() {
                let mut new_metadata = object_id_to_existing_metadata
                    .get(object_id.as_str())
                    .cloned()
                    .unwrap_or_default();
                if let Some((_, xact_id)) = update.last_processed_xact_id {
                    new_metadata.last_processed_xact_id = xact_id;
                }
                out.insert(
                    object_id.to_string(),
                    PostgresObjectMetadata::from_object_metadata(new_metadata),
                );
            }
            out
        };
        let mut query_params: Vec<&(dyn ToSql + Sync)> = Vec::new();
        for object_id in updates.keys() {
            let metadata = updated_metadatas.get(object_id.as_str()).unwrap();
            query_params.push(object_id);
            query_params.push(&metadata.last_processed_xact_id);
        }
        let placeholders: Vec<String> = (0..(query_params.len() / 2))
            .map(|chunk_idx| {
                let start = chunk_idx * 2 + 1;
                format!("(${}::text, ${}::bigint)", start, start + 1,)
            })
            .collect();
        let num_rows_modified = transaction
            .execute_raw(
                &format!(
                    r#"
                    insert into {}(object_id, last_processed_xact_id)
                    values {}
                    on conflict (object_id) do update set
                        last_processed_xact_id = excluded.last_processed_xact_id
                "#,
                    OBJECT_ID_TO_METADATA_TABLE,
                    placeholders.join(", "),
                ),
                query_params.into_iter(),
            )
            .await?;
        if num_rows_modified != updates.len() as u64 {
            return Err(anyhow!("failed to update all segment metadatas"));
        }
        transaction.commit().await?;
        Ok(true)
    }

    #[instrument(err, skip(self), name = "PostgresGlobalStore::purge_object_metadatas")]
    async fn purge_object_metadatas(&self, object_ids: &[FullObjectId]) -> Result<()> {
        time!(self.debug_timer, "purge_object_metadatas");
        if object_ids.is_empty() {
            return Ok(());
        }

        let object_id_strs = object_ids.iter().map(|o| o.to_string()).collect::<Vec<_>>();
        self.get_connection()
            .await?
            .execute_raw(
                format!(
                    "delete from {} where object_id = any($1::text[])",
                    OBJECT_ID_TO_METADATA_TABLE,
                )
                .as_str(),
                &[&object_id_strs],
            )
            .await?;
        Ok(())
    }

    #[instrument(
        err,
        skip(self, segment_ids, ids),
        fields(num_segment_ids = segment_ids.len(), num_ids = ids.len()),
        name = "PostgresGlobalStore::query_id_segment_membership"
    )]
    async fn query_id_segment_membership(
        &self,
        membership_type: IdSegmentMembershipType,
        segment_ids: &[Uuid],
        ids: &[FullRowId<'_, '_>],
    ) -> Result<HashMap<FullRowIdOwned, Uuid>> {
        time!(self.debug_timer, "query_id_segment_membership");
        if segment_ids.is_empty() || ids.is_empty() {
            return Ok(HashMap::default());
        }

        // For certain legacy table types, we only store the un-qualified "id"
        // instead of the fully-qualified FullRowId. In these cases, we can't
        // really know the original object ID which matched so we return all of
        // them. So we build a mapping from id &str -> Vec<FullRowId> so that we
        // can re-expand the result after getting it.
        let mut id_to_full_row_ids = HashMap::new();
        match membership_type {
            IdSegmentMembershipType::RootSpanId => {
                for id in ids {
                    id_to_full_row_ids
                        .entry(id.id)
                        .or_insert_with(Vec::new)
                        .push(id.clone());
                }
            }
            _ => {}
        }

        // NOTE: For fast queries, we have indices on both segment_id and id. We don't trust
        // postgres to use the id indices when we filter by both segment_id and id, and it
        // may be cumbersome to send over all segment_ids when we have hundreds. So instead we
        // filter by just id and then filter by segment_id in memory.
        //
        // Also note that for root span ID queries, we query by the raw IDs,
        // un-qualified by the object ID, so we need separate query params for
        // those.
        let has_full_id = match membership_type {
            IdSegmentMembershipType::RowId => true,
            IdSegmentMembershipType::RootSpanId => false,
        };
        let id_strs: Vec<String> = ids
            .iter()
            .map(|r| {
                if has_full_id {
                    r.to_string()
                } else {
                    r.id.to_string()
                }
            })
            .collect::<HashSet<String>>()
            .into_iter()
            .collect();
        let all_results = join_all(
            id_strs
                .chunks(global_limits().global_store_parallel_query_batch_size)
                .map(|batch| {
                    let id_to_full_row_ids = &id_to_full_row_ids;
                    async move {
                        let col_id_str = match membership_type {
                            IdSegmentMembershipType::RowId => "row_id",
                            IdSegmentMembershipType::RootSpanId => "root_span_id",
                        };
                        let tbl_new_fut = async {
                            let conn = self.get_connection().await?;
                            let row_stream = conn
                                .query_raw(
                                    &format!(
                                        "select {}, segment_id from {} where {} = any($1::text[])",
                                        col_id_str,
                                        match membership_type {
                                            IdSegmentMembershipType::RowId =>
                                                ROW_ID_TO_SEGMENT_ID_TABLE,
                                            IdSegmentMembershipType::RootSpanId =>
                                                ROOT_SPAN_ID_TO_SEGMENT_ID_TABLE,
                                        },
                                        col_id_str
                                    ),
                                    &[batch],
                                )
                                .await?;
                            query_id_segment_membership_process_stream(
                                row_stream,
                                has_full_id,
                                &id_to_full_row_ids,
                            )
                            .await
                        }
                        .instrument(tracing::debug_span!(
                            "query_id_segment_membership inline new",
                            ids = batch.len(),
                            segment_ids = segment_ids.len(),
                        ));
                        let tbl_legacy0_fut = async {
                            let conn = self.get_connection().await?;
                            let row_stream = conn
                                .query_raw(
                                    &format!(
                                        "select {}, segment_id from {} where {} = any($1::text[])",
                                        col_id_str, LEGACY_SEGMENT_ID_TO_ROW_INFO_TABLE, col_id_str
                                    ),
                                    &[batch],
                                )
                                .await?;
                            query_id_segment_membership_process_stream(
                                row_stream,
                                has_full_id,
                                &id_to_full_row_ids,
                            )
                            .await
                        }
                        .instrument(tracing::debug_span!(
                            "query_id_segment_membership inline legacy",
                            ids = batch.len(),
                            segment_ids = segment_ids.len(),
                        ));
                        let (res1, res2) = join!(tbl_new_fut, tbl_legacy0_fut);
                        Ok::<_, util::anyhow::Error>(
                            res1?
                                .into_iter()
                                .chain(res2?.into_iter())
                                .collect::<HashSet<_>>(),
                        )
                    }
                }),
        )
        .await;

        let segment_id_set = segment_ids
            .iter()
            .copied()
            .collect::<std::collections::HashSet<_>>();
        let mut out = HashMap::default();
        for row_batch in all_results {
            for (id, segment_id) in row_batch? {
                if !segment_id_set.contains(&segment_id) {
                    continue;
                }
                if let Some(existing_segment_id) = out.insert(id.clone(), segment_id) {
                    return Err(anyhow!(
                        "{} {} belongs to multiple segments: {} {}",
                        membership_type,
                        id,
                        existing_segment_id,
                        segment_id
                    ));
                }
            }
        }
        Ok(out)
    }

    #[instrument(
        err,
        skip(self, segment_id_to_entries),
        fields(num_entries = segment_id_to_entries.values().flatten().count()),
        name = "PostgresGlobalStore::add_id_segment_membership"
    )]
    async fn add_id_segment_membership(
        &self,
        membership_type: IdSegmentMembershipType,
        segment_id_to_entries: HashMap<Uuid, Vec<FullRowId<'_, '_>>>,
    ) -> Result<()> {
        time!(self.debug_timer, "add_id_segment_membership");
        if segment_id_to_entries.is_empty() {
            return Ok(());
        }
        let mut conn = self.get_connection().await?;
        let transaction = conn.transaction().await?;
        // Expand out all the inputs into a single list so we can chunk it. For
        // legacy tables, we store just the row ID component rather than the
        // FullRowId.
        let has_full_id = match membership_type {
            IdSegmentMembershipType::RowId => true,
            IdSegmentMembershipType::RootSpanId => false,
        };
        let expanded_entries = segment_id_to_entries
            .iter()
            .flat_map(|(segment_id, ids)| {
                ids.iter().map(|id| {
                    (
                        if has_full_id {
                            id.to_string()
                        } else {
                            id.id.to_string()
                        },
                        *segment_id,
                    )
                })
            })
            .collect::<Vec<_>>();
        let max_params_per_query = global_limits().global_store_max_params_per_query;
        for batch in expanded_entries.chunks(max_params_per_query / 2) {
            // Split into two separate lists.
            let mut ids = Vec::new();
            let mut segment_ids = Vec::new();
            for (id, segment_id) in batch {
                segment_ids.push(*segment_id);
                ids.push(id.as_str());
            }

            let col_id_str = match membership_type {
                IdSegmentMembershipType::RowId => "row_id",
                IdSegmentMembershipType::RootSpanId => "root_span_id",
            };
            let num_modified = transaction
                .execute(
                    &format!(
                        r#"
                            insert into {}({}, segment_id)
                            select id, segment_id
                            from unnest($1::text[], $2::uuid[]) as t(id, segment_id)
                        "#,
                        match membership_type {
                            IdSegmentMembershipType::RowId => ROW_ID_TO_SEGMENT_ID_TABLE,
                            IdSegmentMembershipType::RootSpanId => ROOT_SPAN_ID_TO_SEGMENT_ID_TABLE,
                        },
                        col_id_str,
                    ),
                    &[&ids, &segment_ids],
                )
                .await?;
            if num_modified != segment_ids.len() as u64 {
                return Err(anyhow!("failed to insert {}s", membership_type));
            }
        }
        transaction.commit().await?;
        Ok(())
    }

    #[instrument(
        err,
        skip(self),
        name = "PostgresGlobalStore::purge_id_segment_membership"
    )]
    async fn purge_id_segment_membership(&self, segment_ids: &[Uuid]) -> Result<()> {
        time!(self.debug_timer, "purge_id_segment_membership");
        if segment_ids.is_empty() {
            return Ok(());
        }

        let mut conn = self.get_connection().await?;
        let transaction = conn.transaction().await?;
        transaction
            .execute_raw(
                format!(
                    "delete from {} where segment_id = any($1::uuid[])",
                    ROW_ID_TO_SEGMENT_ID_TABLE,
                )
                .as_str(),
                &[&segment_ids],
            )
            .await?;
        transaction
            .execute_raw(
                format!(
                    "delete from {} where segment_id = any($1::uuid[])",
                    ROOT_SPAN_ID_TO_SEGMENT_ID_TABLE,
                )
                .as_str(),
                &[&segment_ids],
            )
            .await?;
        transaction
            .execute_raw(
                format!(
                    "delete from {} where segment_id = any($1::uuid[])",
                    LEGACY_SEGMENT_ID_TO_ROW_INFO_TABLE,
                )
                .as_str(),
                &[&segment_ids],
            )
            .await?;
        transaction.commit().await?;
        Ok(())
    }

    #[instrument(
        err,
        skip(self),
        name = "PostgresGlobalStore::copy_id_segment_membership"
    )]
    async fn copy_id_segment_membership(
        &self,
        src_segment_ids: &[Uuid],
        dst_segment_id: Uuid,
    ) -> Result<()> {
        time!(self.debug_timer, "copy_id_segment_membership");
        if src_segment_ids.is_empty() {
            return Ok(());
        }
        let query_params: [&(dyn ToSql + Sync); 2] = [&dst_segment_id, &src_segment_ids];

        let mut conn = self.get_connection().await?;
        let transaction = conn.transaction().await?;
        transaction
            .execute_raw(
                format!(
                    "insert into {}(segment_id, row_id) select $1::uuid, row_id from {} where segment_id = any($2::uuid[]) on conflict do nothing",
                    ROW_ID_TO_SEGMENT_ID_TABLE, ROW_ID_TO_SEGMENT_ID_TABLE,
                )
                .as_str(),
                query_params.clone(),
            )
            .await?;
        transaction
            .execute_raw(
                format!(
                    "insert into {}(segment_id, row_id) select $1::uuid, row_id from {} where segment_id = any($2::uuid[]) on conflict do nothing",
                    ROW_ID_TO_SEGMENT_ID_TABLE, LEGACY_SEGMENT_ID_TO_ROW_INFO_TABLE,
                )
                .as_str(),
                query_params.clone(),
            )
            .await?;
        transaction
            .execute_raw(
                format!(
                    "insert into {}(segment_id, root_span_id) select $1::uuid, root_span_id from {} where segment_id = any($2::uuid[]) on conflict do nothing",
                    ROOT_SPAN_ID_TO_SEGMENT_ID_TABLE, ROOT_SPAN_ID_TO_SEGMENT_ID_TABLE,
                )
                .as_str(),
                query_params.clone(),
            )
            .await?;
        transaction
            .execute_raw(
                format!(
                    "insert into {}(segment_id, root_span_id) select $1::uuid, root_span_id from {} where segment_id = any($2::uuid[]) on conflict do nothing",
                    ROOT_SPAN_ID_TO_SEGMENT_ID_TABLE, LEGACY_SEGMENT_ID_TO_ROW_INFO_TABLE,
                )
                .as_str(),
                query_params,
            )
            .await?;
        transaction.commit().await?;
        Ok(())
    }

    #[instrument(
        err,
        skip(self),
        name = "PostgresGlobalStore::query_segment_wal_entries_batch"
    )]
    async fn query_segment_wal_entries_batch(
        &self,
        segment_id: Uuid,
        cursor: Option<SegmentWalEntriesCursor>,
        limit: Option<i64>,
        is_compacted_filter: Option<bool>,
    ) -> Result<Vec<SegmentWalEntry>> {
        // (With no cursor)
        //
        //            QUERY PLAN
        // --------------------------------
        //  Limit  (cost=7.40..7.41 rows=1 width=49)
        //    ->  Sort  (cost=7.40..7.41 rows=1 width=49)
        //          Sort Key: xact_id, wal_filename
        //          ->  Index Scan using brainstore_global_store_segment_id__segment_id_wal_filename_idx on brainstore_global_store_segment_id_to_wal_entries  (cost=0.29..7.39 rows=1 width=49)
        //                Index Cond: (segment_id = 'c1b28fe7-5572-4d7c-a9aa-8053f9153877'::uuid)
        //                Filter: (deleted_at IS NULL)
        // (6 rows)
        //
        // (With XactIdGe cursor)
        //
        //            QUERY PLAN
        // --------------------------------
        //  Limit  (cost=0.41..2.43 rows=1 width=49)
        //    ->  Index Scan using brainstore_global_store_segment_id_to_wal_entries_pkey on brainstore_global_store_segment_id_to_wal_entries  (cost=0.41..2.43 rows=1 width=49)
        //          Index Cond: ((segment_id = 'c1b28fe7-5572-4d7c-a9aa-8053f9153877'::uuid) AND (xact_id >= '1000194817061158916'::bigint))
        //          Filter: (deleted_at IS NULL)
        // (4 rows)
        //
        // (With XactIdWalFilenameGt cursor)
        //
        //            QUERY PLAN
        // --------------------------------
        //  Limit  (cost=0.41..2.43 rows=1 width=49)
        //    ->  Index Scan using brainstore_global_store_segment_id_to_wal_entries_pkey on brainstore_global_store_segment_id_to_wal_entries  (cost=0.41..2.43 rows=1 width=49)
        //          Index Cond: ((segment_id = 'c1b28fe7-5572-4d7c-a9aa-8053f9153877'::uuid) AND (ROW(xact_id, wal_filename) > ROW('1000194817061158916'::bigint, '1dd67935-63b0-4941-b231-c827f7f04049'::uuid)))
        //          Filter: (deleted_at IS NULL)
        // (4 rows)

        time!(self.debug_timer, "query_segment_wal_entries_batch");
        // For some reason rust warns about this variable being unread, but we add it to the query
        // params and use it there, so not sure why it's complaining.
        let mut _xact_id_param: i64 = 0;
        let mut query_params: Vec<&(dyn ToSql + Sync)> = Vec::new();
        query_params.push(&segment_id);
        let mut query = format!(
            r#"
                select xact_id, wal_filename, byte_range_start, byte_range_end, is_compacted, digest
                from {}
                where segment_id = ${}::uuid
                and deleted_at is null
            "#,
            SEGMENT_ID_TO_WAL_ENTRIES_TABLE,
            query_params.len()
        );
        match is_compacted_filter {
            Some(true) => {
                write!(&mut query, " and is_compacted")?;
            }
            Some(false) => {
                write!(&mut query, " and not is_compacted")?;
            }
            None => {}
        };
        match &cursor {
            Some(SegmentWalEntriesCursor::XactIdGe(xact_id)) => {
                _xact_id_param = xact_id.0 as i64;
                query_params.push(&_xact_id_param);
                write!(
                    &mut query,
                    " and xact_id >= ${}::bigint",
                    query_params.len()
                )?;
            }
            Some(SegmentWalEntriesCursor::XactIdWalFilenameGt {
                xact_id,
                wal_filename,
            }) => {
                _xact_id_param = xact_id.0 as i64;
                query_params.push(&_xact_id_param);
                query_params.push(wal_filename);
                write!(
                    &mut query,
                    " and (xact_id, wal_filename) > (${}::bigint, ${}::uuid)",
                    query_params.len() - 1,
                    query_params.len()
                )?;
            }
            None => {}
        };
        write!(&mut query, " order by xact_id, wal_filename")?;
        match &limit {
            Some(limit) => {
                query_params.push(limit);
                write!(&mut query, " limit ${}::bigint", query_params.len())?;
            }
            None => {}
        };
        let res = self
            .get_connection()
            .await?
            .query_raw(query.as_str(), query_params.into_iter())
            .await?;
        res.map(|row| {
            let row = row?;
            let xact_id = get_xact_id(&row, 0);
            let wal_filename: Uuid = row.get(1);
            let byte_range_start: i64 = row.get(2);
            let byte_range_end: i64 = row.get(3);
            let is_compacted: bool = row.get(4);
            let digest: Option<i64> = row.get(5);
            Ok(SegmentWalEntry {
                xact_id,
                wal_filename,
                byte_range_start: byte_range_start as usize,
                byte_range_end: byte_range_end as usize,
                is_compacted,
                digest,
                deleted_at: None,
            })
        })
        .try_collect()
        .await
    }

    #[instrument(
        err
        skip(self),
        name = "PostgresGlobalStore::query_segment_wal_entries_existence",
    )]
    async fn query_segment_wal_entries_existence(
        &self,
        segment_id_cursors: &[(Uuid, Option<SegmentWalEntriesCursor>)],
        is_compacted_filter: Option<bool>,
    ) -> Result<Vec<bool>> {
        time!(self.debug_timer, "query_segment_wal_entries_existence");

        if segment_id_cursors.is_empty() {
            return Ok(Vec::new());
        }

        let cursor_idx_params: Vec<i64> = segment_id_cursors
            .iter()
            .enumerate()
            .map(|(idx, _)| idx as i64)
            .collect();
        let cursor_xact_id_params = {
            let mut params: Vec<i64> = Vec::new();
            for (_, cursor) in segment_id_cursors.iter() {
                match cursor {
                    None => {
                        params.push(0);
                    }
                    Some(SegmentWalEntriesCursor::XactIdGe(xact_id)) => {
                        params.push(xact_id.0 as i64);
                    }
                    Some(SegmentWalEntriesCursor::XactIdWalFilenameGt {
                        xact_id,
                        wal_filename: _,
                    }) => {
                        params.push(xact_id.0 as i64);
                    }
                }
            }
            params
        };

        let mut no_cursor_idxs: Vec<i64> = Vec::new();
        let mut no_cursor_segment_ids: Vec<Uuid> = Vec::new();

        let mut xact_id_cursor_idxs: Vec<i64> = Vec::new();
        let mut xact_id_cursor_segment_ids: Vec<Uuid> = Vec::new();
        let mut xact_id_cursor_xact_ids: Vec<i64> = Vec::new();

        let mut xact_id_wal_filename_cursor_idxs: Vec<i64> = Vec::new();
        let mut xact_id_wal_filename_cursor_segment_ids: Vec<Uuid> = Vec::new();
        let mut xact_id_wal_filename_cursor_xact_ids: Vec<i64> = Vec::new();
        let mut xact_id_wal_filename_cursor_wal_filenames: Vec<Uuid> = Vec::new();

        for (idx, (segment_id, cursor)) in segment_id_cursors.iter().enumerate() {
            let cursor_idx = cursor_idx_params[idx];
            match cursor {
                None => {
                    no_cursor_idxs.push(cursor_idx);
                    no_cursor_segment_ids.push(*segment_id);
                }
                Some(SegmentWalEntriesCursor::XactIdGe(_)) => {
                    xact_id_cursor_idxs.push(cursor_idx);
                    xact_id_cursor_segment_ids.push(*segment_id);
                    xact_id_cursor_xact_ids.push(cursor_xact_id_params[idx]);
                }
                Some(SegmentWalEntriesCursor::XactIdWalFilenameGt {
                    xact_id: _,
                    wal_filename,
                }) => {
                    xact_id_wal_filename_cursor_idxs.push(cursor_idx);
                    xact_id_wal_filename_cursor_segment_ids.push(*segment_id);
                    xact_id_wal_filename_cursor_xact_ids.push(cursor_xact_id_params[idx]);
                    xact_id_wal_filename_cursor_wal_filenames.push(*wal_filename);
                }
            }
        }

        let mut select_components: Vec<String> = Vec::new();
        let is_compacted_filter_str = match is_compacted_filter {
            Some(true) => "and is_compacted",
            Some(false) => "and not is_compacted",
            None => "",
        };
        let mut query_params: Vec<&(dyn ToSql + Sync)> = Vec::new();
        if !no_cursor_idxs.is_empty() {
            query_params.push(&no_cursor_idxs);
            query_params.push(&no_cursor_segment_ids);
            select_components.push(format!(
                r#"select
                        cursor_idx,
                        exists(
                            select 1 from brainstore_global_store_segment_id_to_wal_entries b
                            where b.segment_id = i.segment_id
                            and b.deleted_at is null
                            {}
                        )
                    from unnest(${}::bigint[], ${}::uuid[]) i(cursor_idx, segment_id)
                "#,
                is_compacted_filter_str,
                query_params.len() - 1,
                query_params.len()
            ));
        }
        if !xact_id_cursor_idxs.is_empty() {
            query_params.push(&xact_id_cursor_idxs);
            query_params.push(&xact_id_cursor_segment_ids);
            query_params.push(&xact_id_cursor_xact_ids);
            select_components.push(format!(
                r#"select
                        cursor_idx,
                        exists(
                            select 1 from brainstore_global_store_segment_id_to_wal_entries b
                            where b.segment_id = i.segment_id
                            and b.xact_id >= i.xact_id
                            and b.deleted_at is null
                            {}
                        )
                    from unnest(${}::bigint[], ${}::uuid[], ${}::bigint[]) i(cursor_idx, segment_id, xact_id)
                "#,
                is_compacted_filter_str,
                query_params.len() - 2,
                query_params.len() - 1,
                query_params.len()
            ));
        }
        if !xact_id_wal_filename_cursor_idxs.is_empty() {
            query_params.push(&xact_id_wal_filename_cursor_idxs);
            query_params.push(&xact_id_wal_filename_cursor_segment_ids);
            query_params.push(&xact_id_wal_filename_cursor_xact_ids);
            query_params.push(&xact_id_wal_filename_cursor_wal_filenames);
            select_components.push(format!(
                r#"select
                        cursor_idx,
                        exists(
                            select 1 from brainstore_global_store_segment_id_to_wal_entries b
                            where b.segment_id = i.segment_id
                            and (b.xact_id, b.wal_filename) > (i.xact_id, i.wal_filename)
                            and b.deleted_at is null
                            {}
                        )
                    from unnest(${}::bigint[], ${}::uuid[], ${}::bigint[], ${}::uuid[]) i(cursor_idx, segment_id, xact_id, wal_filename)
                "#,
                is_compacted_filter_str,
                query_params.len() - 3,
                query_params.len() - 2,
                query_params.len() - 1,
                query_params.len()
            ));
        }

        let query = select_components.join("\nunion all\n");
        // We run the query with `enable_seqscan=off`, to try to ensure that postgres does an index
        // scan for each of the exists queries.
        let mut conn = self.get_connection().await?;
        let transaction = conn.transaction().await?;
        transaction
            .execute("set local enable_seqscan=off", &[])
            .await?;
        let res = transaction.query_raw(query.as_str(), query_params).await?;
        pin_mut!(res);
        let mut ret: Vec<bool> = vec![false; segment_id_cursors.len()];
        while let Some(row) = res.next().await {
            let row = row?;
            let cursor_idx: i64 = row.get(0);
            let exists: bool = row.get(1);
            ret[cursor_idx as usize] = exists;
        }
        Ok(ret)
    }

    #[instrument(
        skip(self),
        name = "PostgresGlobalStore::query_segment_wal_entries_xact_id_statistic"
    )]
    async fn query_segment_wal_entries_xact_id_statistic(
        &self,
        segment_ids: &[Uuid],
        statistic: SegmentWalEntriesXactIdStatistic,
        is_compacted_filter: Option<bool>,
    ) -> Result<Vec<Option<TransactionId>>> {
        if segment_ids.is_empty() {
            return Ok(Vec::new());
        }

        // The lateral join allows postgres to use the (segment_id, xact_id) index on the
        // segment_id_to_wal_entries table.
        //
        //                                      QUERY PLAN
        // ------------------------------------------------------------------------------------------------
        //  Nested Loop  (cost=0.41..9.01 rows=2 width=24)
        //    ->  ProjectSet  (cost=0.00..0.03 rows=2 width=16)
        //          ->  Result  (cost=0.00..0.01 rows=1 width=0)
        //    ->  Limit  (cost=0.41..4.46 rows=1 width=8)
        //          ->  Index Scan Backward using brainstore_global_store_segme_segment_id_xact_id_wal_filena_idx on brainstore_global_store_segment_id_to_wal_entries  (cost=0.41..4.46 rows=1 width=8)
        //                Index Cond: (segment_id = (unnest('{c1b28fe7-5572-4d7c-a9aa-8053f9153877,a2b28fe7-5572-4d7c-a9aa-8053f9153878}'::uuid[])))
        //                Filter: (deleted_at IS NULL)
        // (7 rows)
        let query = format!(
            r#"
                SELECT s.segment_id,
                       e.xact_id
                FROM (
                    SELECT unnest($1::uuid[]) AS segment_id
                ) s
                CROSS JOIN LATERAL (
                    SELECT xact_id
                    FROM {}
                    WHERE segment_id = s.segment_id and deleted_at is null {}
                    ORDER BY xact_id {}
                    LIMIT 1
                ) e
            "#,
            SEGMENT_ID_TO_WAL_ENTRIES_TABLE,
            match is_compacted_filter {
                Some(true) => "AND is_compacted",
                Some(false) => "AND NOT is_compacted",
                None => "",
            },
            match statistic {
                SegmentWalEntriesXactIdStatistic::Min => "ASC",
                SegmentWalEntriesXactIdStatistic::Max => "DESC",
            },
        );

        let results = self
            .get_connection()
            .await?
            .query_raw(query.as_str(), &[&segment_ids])
            .await?;
        let results_map: HashMap<Uuid, TransactionId> = results
            .map(|row| {
                let row = row?;
                let row_segment_id: Uuid = row.get(0);
                let xact_id = get_xact_id(&row, 1);
                Ok::<_, util::anyhow::Error>((row_segment_id, xact_id))
            })
            .try_collect()
            .await?;
        Ok(segment_ids
            .iter()
            .map(|segment_id| results_map.get(segment_id).copied())
            .collect())
    }

    #[instrument(
        err,
        skip(self, segment_id_to_wal_filename_to_entries),
        fields(num_entries = segment_id_to_wal_filename_to_entries.values().flat_map(|m| m.values()).count()),
        name = "PostgresGlobalStore::upsert_segment_wal_entries"
    )]
    async fn upsert_segment_wal_entries(
        &self,
        segment_id_to_wal_filename_to_entries: HashMap<
            Uuid,
            HashMap<Uuid, Vec<UpsertSegmentWalEntry>>,
        >,
    ) -> Result<u64> {
        if segment_id_to_wal_filename_to_entries.is_empty() {
            return Ok(0);
        }
        let segment_ids = segment_id_to_wal_filename_to_entries
            .keys()
            .cloned()
            .collect::<Vec<_>>();

        // Check that all xact_ids are unique in each inner map.
        for (segment_id, wal_map) in &segment_id_to_wal_filename_to_entries {
            for (wal_filename, entries) in wal_map {
                let mut xact_ids = HashSet::new();
                for entry in entries {
                    if !xact_ids.insert(entry.xact_id) {
                        return Err(anyhow!(
                            "Duplicate xact_id found in segment {} wal filename {}: {}",
                            segment_id,
                            wal_filename,
                            entry.xact_id
                        ));
                    }
                }
            }
        }

        let mut conn = self.get_connection().await?;
        let transaction = conn.transaction().await?;

        let mut expanded_entries = Vec::new();
        for (segment_id, wal_map) in segment_id_to_wal_filename_to_entries {
            for (wal_filename, entries) in wal_map {
                expanded_entries.extend(entries.into_iter().map(|e| {
                    (
                        segment_id,
                        wal_filename,
                        e.xact_id.0 as i64,
                        e.byte_range_start as i64,
                        e.byte_range_end as i64,
                        e.is_compacted,
                        e.digest,
                    )
                }));
            }
        }
        let mut num_non_ignored_entries: u64 = 0;
        let max_params_per_query = global_limits().global_store_max_params_per_query;
        for batch in expanded_entries.chunks(max_params_per_query / 7) {
            // Split the batch into separate lists.
            let mut segment_ids: Vec<Uuid> = Vec::new();
            let mut wal_filenames: Vec<Uuid> = Vec::new();
            let mut xact_ids: Vec<i64> = Vec::new();
            let mut byte_range_starts: Vec<i64> = Vec::new();
            let mut byte_range_ends: Vec<i64> = Vec::new();
            let mut is_compacteds: Vec<bool> = Vec::new();
            let mut digests: Vec<Option<i64>> = Vec::new();
            for (
                segment_id,
                wal_filename,
                xact_id,
                byte_range_start,
                byte_range_end,
                is_compacted,
                digest,
            ) in batch
            {
                segment_ids.push(*segment_id);
                wal_filenames.push(*wal_filename);
                xact_ids.push(*xact_id);
                byte_range_starts.push(*byte_range_start);
                byte_range_ends.push(*byte_range_end);
                is_compacteds.push(*is_compacted);
                digests.push(*digest);
            }
            let mut query_params: Vec<&(dyn ToSql + Sync)> = Vec::new();
            query_params.push(&segment_ids);
            query_params.push(&xact_ids);
            query_params.push(&wal_filenames);
            query_params.push(&byte_range_starts);
            query_params.push(&byte_range_ends);
            query_params.push(&is_compacteds);
            query_params.push(&digests);
            let num_inserted = transaction
                .execute(
                    &format!(
                        r#"
                        with
                        unnested_rows as (
                            select *
                            from unnest($1::uuid[], $2::bigint[], $3::uuid[], $4::bigint[], $5::bigint[], $6::bool[], $7::bigint[])
                                as t(segment_id, xact_id, wal_filename, byte_range_start, byte_range_end, is_compacted, digest)
                        ),
                        rows_to_insert as (
                            select * from unnested_rows
                            where not exists (
                                select 1 from {}
                                where
                                    {}.segment_id = unnested_rows.segment_id and
                                    {}.xact_id = unnested_rows.xact_id and
                                    {}.wal_filename != unnested_rows.wal_filename and
                                    {}.digest = unnested_rows.digest
                            )
                        )
                        insert into {}(segment_id, xact_id, wal_filename, byte_range_start, byte_range_end, is_compacted, digest)
                        select * from rows_to_insert
                        on conflict (segment_id, xact_id, wal_filename) do update set is_compacted = excluded.is_compacted
                    "#,
                        SEGMENT_ID_TO_WAL_ENTRIES_TABLE,
                        SEGMENT_ID_TO_WAL_ENTRIES_TABLE,
                        SEGMENT_ID_TO_WAL_ENTRIES_TABLE,
                        SEGMENT_ID_TO_WAL_ENTRIES_TABLE,
                        SEGMENT_ID_TO_WAL_ENTRIES_TABLE,
                        SEGMENT_ID_TO_WAL_ENTRIES_TABLE,
                    ),
                    &query_params,
                )
                .await?;
            num_non_ignored_entries += num_inserted as u64;
        }
        transaction.commit().await?;

        // Refresh the earliest_uncompacted_xact_id for each segment.
        for batch in segment_ids.chunks(max_params_per_query) {
            self.refresh_segment_id_earliest_uncompacted_xact_id(batch)
                .await?;
        }

        Ok(num_non_ignored_entries)
    }

    #[instrument(
        err,
        skip(self, segment_id_xact_id_wal_filenames),
        fields(num_entries = segment_id_xact_id_wal_filenames.len()),
        name = "PostgresGlobalStore::update_segment_wal_entries_is_compacted_non_atomic"
    )]
    async fn update_segment_wal_entries_is_compacted_non_atomic(
        &self,
        segment_id_xact_id_wal_filenames: &[(Uuid, TransactionId, Uuid)],
        is_compacted: bool,
    ) -> Result<()> {
        if segment_id_xact_id_wal_filenames.is_empty() {
            return Ok(());
        }

        let batch_size = std::cmp::min(
            global_limits().global_store_non_atomic_insert_batch_size,
            global_limits().global_store_max_params_per_query / 3,
        );
        for batch in segment_id_xact_id_wal_filenames.chunks(batch_size) {
            let mut segment_ids = Vec::new();
            let mut xact_ids = Vec::new();
            let mut wal_filenames = Vec::new();
            for (segment_id, xact_id, wal_filename) in batch {
                segment_ids.push(*segment_id);
                xact_ids.push(xact_id.0 as i64);
                wal_filenames.push(*wal_filename);
            }
            let query_params: Vec<&(dyn ToSql + Sync)> =
                vec![&segment_ids, &xact_ids, &wal_filenames, &is_compacted];
            let num_rows = self
                .get_connection()
                .await?
                .execute(
                    &format!(
                        r#"
                        with all_rows as (
                            select *
                            from unnest($1::uuid[], $2::bigint[], $3::uuid[])
                                as t(segment_id, xact_id, wal_filename)
                        )
                        update {} set is_compacted = $4
                        where (segment_id, xact_id, wal_filename) in (select * from all_rows)
                    "#,
                        SEGMENT_ID_TO_WAL_ENTRIES_TABLE,
                    ),
                    &query_params,
                )
                .await?;
            let unique_segment_ids = segment_ids
                .iter()
                .cloned()
                .collect::<HashSet<_>>()
                .into_iter()
                .collect::<Vec<_>>();
            self.refresh_segment_id_earliest_uncompacted_xact_id(&unique_segment_ids)
                .await?;
            if num_rows != batch.len() as u64 {
                return Err(anyhow!("failed to update all segment wal entries in batch"));
            }
        }

        Ok(())
    }

    #[instrument(
        err,
        skip(self, segment_ids),
        fields(num_entries = segment_ids.len()),
        name = "PostgresGlobalStore::update_all_segment_wal_entries_is_compacted_non_atomic"
    )]
    async fn update_all_segment_wal_entries_is_compacted_non_atomic(
        &self,
        segment_ids: &[Uuid],
        is_compacted: bool,
    ) -> Result<()> {
        if segment_ids.is_empty() {
            return Ok(());
        }
        let update_query = format!(
            "update {} set is_compacted = {} where segment_id = any($1::uuid[])",
            SEGMENT_ID_TO_WAL_ENTRIES_TABLE,
            if is_compacted { "true" } else { "false" },
        );
        self.get_connection()
            .await?
            .execute_raw(&update_query, &[&segment_ids])
            .await?;
        self.refresh_segment_id_earliest_uncompacted_xact_id(segment_ids)
            .await?;
        Ok(())
    }

    #[instrument(
        err,
        skip(self),
        name = "PostgresGlobalStore::purge_segment_wal_entries"
    )]
    async fn purge_segment_wal_entries(&self, segment_ids: &[Uuid]) -> Result<()> {
        time!(self.debug_timer, "purge_segment_wal_entries");
        if segment_ids.is_empty() {
            return Ok(());
        }

        self.get_connection()
            .await?
            .execute_raw(
                format!(
                    "delete from {} where segment_id = any($1::uuid[])",
                    SEGMENT_ID_TO_WAL_ENTRIES_TABLE,
                )
                .as_str(),
                &[&segment_ids],
            )
            .await?;
        Ok(())
    }

    #[instrument(
        err,
        skip(self),
        name = "PostgresGlobalStore::query_last_index_operations"
    )]
    async fn query_last_index_operations(
        &self,
        segment_ids: &[Uuid],
    ) -> Result<Vec<Option<LastIndexOperationResult>>> {
        if segment_ids.is_empty() {
            return Ok(Vec::new());
        }

        let rows = self
            .get_connection()
            .await?
            .query_raw(
                format!(
                    "select segment_id, start, last_updated, current_op_token, operation from {} where segment_id = any($1::uuid[])",
                    SEGMENT_ID_TO_LAST_INDEX_OPERATION_TABLE,
                )
                .as_str(),
                &[&segment_ids],
            )
            .await?;

        let mut segment_id_to_last_index_operation: HashMap<_, _> = rows
            .map(|row| {
                let row = row?;
                let segment_id: Uuid = row.get(0);
                let index_operation: LastIndexOperation = serde_json::from_value(row.get(4))?;
                Ok::<_, util::anyhow::Error>((
                    segment_id,
                    LastIndexOperationResult {
                        start: row.get(1),
                        last_updated: row.get(2),
                        current_op_token: row.get(3),
                        operation: index_operation,
                    },
                ))
            })
            .try_collect()
            .await?;

        let ret = segment_ids
            .iter()
            .map(|segment_id| segment_id_to_last_index_operation.remove(segment_id))
            .collect();
        Ok(ret)
    }

    #[instrument(
        err,
        skip(self),
        name = "PostgresGlobalStore::copy_segment_wal_entries"
    )]
    async fn copy_segment_wal_entries(
        &self,
        src_segment_ids: &[Uuid],
        dst_segment_id: Uuid,
    ) -> Result<()> {
        time!(self.debug_timer, "copy_segment_wal_entries");
        if src_segment_ids.is_empty() {
            return Ok(());
        }
        let mut query_params: Vec<&(dyn ToSql + Sync)> = Vec::new();
        query_params.push(&dst_segment_id);
        query_params.push(&src_segment_ids);

        self.get_connection()
            .await?
            .execute_raw(
                format!(
                    r#"
                        insert into {}(segment_id, xact_id, wal_filename, byte_range_start, byte_range_end, is_compacted, digest, deleted_at)
                        select $1::uuid, xact_id, wal_filename, byte_range_start, byte_range_end, is_compacted, digest, deleted_at from {}
                        where segment_id = any($2::uuid[])
                        on conflict (segment_id, xact_id, wal_filename) do update set is_compacted = excluded.is_compacted
                    "#,
                    SEGMENT_ID_TO_WAL_ENTRIES_TABLE, SEGMENT_ID_TO_WAL_ENTRIES_TABLE,
                )
                .as_str(),
                query_params,
            )
            .await?;
        Ok(())
    }

    #[instrument(
        err,
        skip(self),
        name = "PostgresGlobalStore::upsert_last_index_operation"
    )]
    async fn upsert_last_index_operation(
        &self,
        segment_id: Uuid,
        operation: LastIndexOperation,
        op_token: Uuid,
        op_token_opts: LastIndexOpTokenOpts,
    ) -> Result<()> {
        let mut params: Vec<&(dyn ToSql + Sync)> = vec![&segment_id];
        let operation_json = serde_json::to_value(&operation)?;
        params.push(&operation_json);
        params.push(&op_token);
        const TBL: &str = SEGMENT_ID_TO_LAST_INDEX_OPERATION_TABLE;

        let mut query = format!(
            r#"insert into {} (segment_id, start, last_updated, operation, current_op_token) values
                ($1::uuid, NOW(), NOW(), $2::jsonb, $3::uuid)
                on conflict (segment_id) do update
                set"#,
            TBL
        );
        let set_start_expr = format!(
            "(case when {}.current_op_token is distinct from excluded.current_op_token then excluded.start else {}.start end)",
            TBL, TBL);
        let set_current_op_token_expr = if op_token_opts.clear_value {
            "null"
        } else {
            "excluded.current_op_token"
        };
        let should_update_expr: String = if op_token_opts.always_update {
            "true".to_string()
        } else {
            format!(
                "{}.current_op_token is null or {}.current_op_token = excluded.current_op_token",
                TBL, TBL
            )
        };
        let mut add_set_expr = |col: &str, set_expr: &str, is_first: bool| {
            write!(
                query,
                "{}{} = case when {} then {} else {}.{} end",
                if is_first { "\n" } else { ",\n " },
                col,
                should_update_expr,
                set_expr,
                TBL,
                col
            )
            .unwrap();
        };
        add_set_expr("start", &set_start_expr, true);
        add_set_expr("last_updated", "excluded.last_updated", false);
        add_set_expr("operation", "excluded.operation", false);
        add_set_expr("current_op_token", set_current_op_token_expr, false);

        let conn = self.get_connection().await?;
        conn.execute_raw(&query, params.into_iter()).await?;
        Ok(())
    }

    #[instrument(
        err,
        skip(self),
        name = "PostgresGlobalStore::bump_last_index_operation_updated_ts"
    )]
    async fn bump_last_index_operation_updated_ts(
        &self,
        segment_id: Uuid,
        op_token: Uuid,
    ) -> Result<()> {
        let query = format!(
            r#"
            UPDATE {}
            SET last_updated = NOW()
            WHERE segment_id = $1::uuid
            AND current_op_token = $2::uuid
            "#,
            SEGMENT_ID_TO_LAST_INDEX_OPERATION_TABLE
        );

        let conn = self.get_connection().await?;
        conn.execute_raw(&query, &[&segment_id, &op_token]).await?;
        Ok(())
    }

    #[instrument(err, skip(self), name = "PostgresGlobalStore::query_field_statistics")]
    async fn query_field_statistics(
        &self,
        segment_ids: &[Uuid],
        field_names: &[&str],
    ) -> Result<HashMap<Uuid, HashMap<String, SegmentFieldStatistics>>> {
        if segment_ids.is_empty() || field_names.is_empty() {
            return Ok(HashMap::new());
        }

        let mut query_params: Vec<&(dyn ToSql + Sync)> = Vec::new();
        query_params.push(&segment_ids);
        query_params.push(&field_names);
        let rows = self
            .get_connection()
            .await?
            .query_raw(
                r#"
                SELECT segment_id, field_name, min_u64, max_u64
                FROM brainstore_global_store_segment_id_to_column_statistics
                WHERE segment_id = any($1::uuid[]) AND field_name = any($2::text[])
                "#,
                query_params,
            )
            .await?;

        let mut result: HashMap<Uuid, HashMap<String, SegmentFieldStatistics>> = HashMap::new();

        pin_mut!(rows);
        while let Some(row) = rows.next().await {
            let row = row?;
            let segment_id: Uuid = row.get(0);
            let field_name: String = row.get(1);
            let stats = PostgresSegmentFieldStatistics::from_row(&row)?;
            if let Some(field_stats) = stats.field_statistics()? {
                result
                    .entry(segment_id)
                    .or_default()
                    .insert(field_name, field_stats);
            }
        }
        Ok(result)
    }

    #[instrument(err, skip(self), name = "PostgresGlobalStore::upsert_field_statistics")]
    async fn upsert_field_statistics(
        &self,
        segment_id_field_name_statistics: Vec<(Uuid, &str, SegmentFieldStatistics)>,
    ) -> Result<()> {
        if segment_id_field_name_statistics.is_empty() {
            return Ok(());
        }

        let mut conn = self.get_connection().await?;
        let mut transaction = conn.transaction().await?;
        self.upsert_field_statistics_within_tx(segment_id_field_name_statistics, &mut transaction)
            .await?;
        transaction.commit().await?;
        Ok(())
    }

    #[instrument(
        err,
        skip(self),
        name = "PostgresGlobalStore::upsert_segment_metadatas_and_field_statistics"
    )]
    async fn upsert_segment_metadatas_and_field_statistics(
        &self,
        updates: HashMap<Uuid, (SegmentMetadataUpdate, Vec<(&str, SegmentFieldStatistics)>)>,
    ) -> Result<bool> {
        let mut segment_ids = Vec::new();
        let mut metadata_updates = HashMap::new();
        let mut field_statistics_updates = Vec::new();
        for (segment_id, (metadata_update, field_statistics)) in updates {
            segment_ids.push(segment_id);
            metadata_updates.insert(segment_id, metadata_update);
            field_statistics_updates.extend(
                field_statistics
                    .iter()
                    .map(|(field_name, statistics)| (segment_id, *field_name, statistics.clone())),
            );
        }

        let mut conn = self.get_connection().await?;
        let mut transaction = conn.transaction().await?;
        let applied = self
            .upsert_segment_metadatas_within_tx(metadata_updates, &mut transaction)
            .await?;
        if !applied {
            return Ok(false);
        }
        self.upsert_field_statistics_within_tx(field_statistics_updates, &mut transaction)
            .await?;
        self.upsert_segment_last_written_ts_within_tx(
            &segment_ids,
            None, // Defaults to current timestamp
            &mut transaction,
        )
        .await?;
        transaction.commit().await?;
        Ok(true)
    }

    #[instrument(
        err,
        skip(self, segment_id, wal_filenames, treat_files_as_purged_if_soft_deleted_seconds_ago),
        fields(
            segment_id = %segment_id,
            num_wal_filenames = wal_filenames.len(),
        ),
        name = "PostgresGlobalStore::query_purged_wal_filenames"
    )]
    async fn query_purged_wal_filenames(
        &self,
        segment_id: Uuid,
        wal_filenames: &[Uuid],
        treat_files_as_purged_if_soft_deleted_seconds_ago: Option<i64>,
    ) -> Result<Vec<Uuid>> {
        // NOTE: I had to write the query this way instead of using NOT EXISTS to get postgres to
        // use the (segment_id, wal_filename) index.
        //
        // EXPLAIN
        // WITH input_files AS (
        //   SELECT wal_filename
        //   FROM unnest(
        //     ARRAY[
        //       '2aa22222-aaaa-4444-bbbb-123456789abd'::uuid,
        //       '4f1e259b-7f9a-4d05-9849-98e7e5e6a209'::uuid
        //     ]
        //   ) AS wal_filename
        // )
        // SELECT i.wal_filename
        // FROM input_files AS i
        // LEFT JOIN LATERAL (
        //   SELECT 1
        //   FROM brainstore_global_store_segment_id_to_wal_entries e
        //   WHERE e.segment_id = 'a891d036-804e-4b4d-a3c5-05fdc30864b8'::uuid
        //     AND e.wal_filename = i.wal_filename
        //   LIMIT 1
        // ) AS hit ON TRUE
        // WHERE hit IS NULL;
        //
        //  Nested Loop Left Join  (cost=0.28..4.66 rows=1 width=16)
        //    Filter: (hit.* IS NULL)
        //    ->  Function Scan on unnest wal_filename  (cost=0.00..0.02 rows=2 width=16)
        //    ->  Subquery Scan on hit  (cost=0.28..2.31 rows=1 width=24)
        //          ->  Limit  (cost=0.28..2.30 rows=1 width=4)
        //                ->  Index Only Scan using brainstore_global_store_segment_id__segment_id_wal_filename_idx on brainstore_global_store_segment_id_to_wal_entries e  (cost=0.28..2.30 rows=1 width=4)
        //                      Index Cond: ((segment_id = 'a891d036-804e-4b4d-a3c5-05fdc30864b8'::uuid) AND (wal_filename = wal_filename.wal_filename))

        if wal_filenames.is_empty() {
            return Ok(Vec::new());
        }

        let mut params: Vec<&(dyn ToSql + Sync)> = Vec::new();
        params.push(&wal_filenames);
        params.push(&segment_id);

        let mut _deleted_at_seconds: i64 = 0;
        let deleted_at_filter = match treat_files_as_purged_if_soft_deleted_seconds_ago {
            Some(seconds) => {
                _deleted_at_seconds = seconds;
                params.push(&_deleted_at_seconds);
                "e.deleted_at IS NULL OR e.deleted_at >= NOW() - make_interval(secs => $3::bigint)"
            }
            None => "true",
        };

        let query = format!(
            r#"
            WITH input_files AS (
                SELECT wal_filename
                FROM unnest($1::uuid[]) AS wal_filename
            )
            SELECT i.wal_filename
            FROM input_files AS i
            LEFT JOIN LATERAL (
                SELECT 1
                FROM {table} e
                WHERE e.segment_id = $2
                  AND e.wal_filename = i.wal_filename
                  AND {deleted_at_filter}
                LIMIT 1
            ) AS hit ON TRUE
            WHERE hit IS NULL;
            "#,
            table = SEGMENT_ID_TO_WAL_ENTRIES_TABLE,
            deleted_at_filter = deleted_at_filter,
        );

        let rows = self.get_connection().await?.query(&query, &params).await?;
        Ok(rows.iter().map(|row| row.get::<_, Uuid>(0)).collect())
    }

    /// Count the number of WAL entries with xact_id less than the given transaction ID.
    async fn count_segment_wal_entries_up_to_xact_id(
        &self,
        segment_ids: &[Uuid],
        xact_id: TransactionId,
    ) -> Result<u64> {
        // Query plan:
        //
        // EXPLAIN
        // SELECT COUNT(*) FROM brainstore_global_store_segment_id_to_wal_entries
        // WHERE segment_id = ANY(ARRAY['c1b28fe7-5572-4d7c-a9aa-8053f9153877']::uuid[])
        // AND xact_id < 1000194817061158916
        // AND deleted_at is null;
        //
        // Aggregate  (cost=6.52..6.53 rows=1 width=8)
        // ->  Index Scan using brainstore_global_store_segment_id_to_wal_entries_pkey on brainstore_global_store_segment_id_to_wal_entries  (cost=0.41..6.51 rows=1 width=0)
        //       Index Cond: ((segment_id = ANY ('{c1b28fe7-5572-4d7c-a9aa-8053f9153877}'::uuid[])) AND (xact_id < '1000194817061158916'::bigint))
        //       Filter: (deleted_at IS NULL)
        // (4 rows)

        if segment_ids.is_empty() {
            return Ok(0);
        }

        let xact_id_i64 = xact_id.0 as i64;
        let mut query_params: Vec<&(dyn ToSql + Sync)> = Vec::new();
        query_params.push(&segment_ids);
        query_params.push(&xact_id_i64);

        let row = self
            .get_connection()
            .await?
            .query_one(
                format!(
                    r#"SELECT COUNT(*) FROM {}
                    WHERE segment_id = ANY($1::uuid[])
                    AND xact_id < $2::bigint
                    AND deleted_at is null
                    "#,
                    SEGMENT_ID_TO_WAL_ENTRIES_TABLE,
                )
                .as_str(),
                &query_params,
            )
            .await?;

        Ok(row.get::<_, i64>(0) as u64)
    }

    /// Mark WAL entries as deleted if they are older than the input xact_id.
    async fn delete_segment_wal_entries_up_to_xact_id(
        &self,
        segment_ids: &[Uuid],
        xact_id: TransactionId,
    ) -> Result<u64> {
        // Query plan:
        //
        //  Update on brainstore_global_store_segment_id_to_wal_entries  (cost=0.41..13.03 rows=0 width=0)
        //    ->  Index Scan using brainstore_global_store_segment_id_to_wal_entries_pkey on brainstore_global_store_segment_id_to_wal_entries  (cost=0.41..13.03 rows=1 width=14)
        //          Index Cond: ((segment_id = ANY ('{c1b28fe7-5572-4d7c-a9aa-8053f9153877,a2b28fe7-5572-4d7c-a9aa-8053f9153878}'::uuid[])) AND (xact_id < '1000194817061158916'::bigint))
        //          Filter: (deleted_at IS NULL)
        // (4 rows)
        if segment_ids.is_empty() {
            return Ok(0);
        }

        let segment_ids_vec: Vec<Uuid> = segment_ids.to_vec();
        let xact_id_i64 = xact_id.0 as i64;

        let mut query_params: Vec<&(dyn ToSql + Sync)> = Vec::new();
        query_params.push(&segment_ids_vec);
        query_params.push(&xact_id_i64);

        let updated_count = self
            .get_connection()
            .await?
            .execute_raw(
                format!(
                    r#"UPDATE {}
                    SET deleted_at = now()
                    WHERE segment_id = ANY($1::uuid[])
                    AND xact_id < $2::bigint
                    AND deleted_at is null
                    "#,
                    SEGMENT_ID_TO_WAL_ENTRIES_TABLE,
                )
                .as_str(),
                query_params.into_iter(),
            )
            .await?;

        Ok(updated_count as u64)
    }

    /// Count WAL entries that were soft-deleted more than `expiration_seconds` ago.
    /// (exactly what `purge_deleted_segment_wal_entries` would delete).
    async fn count_deleted_segment_wal_entries(
        &self,
        segment_ids: &[Uuid],
        expiration_seconds: i64,
    ) -> Result<u64> {
        // QUERY PLAN
        // -------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
        //  Aggregate  (cost=4.61..4.62 rows=1 width=8)
        //    ->  Index Scan using brainstore_global_store_segment_id__segment_id_wal_filename_idx on brainstore_global_store_segment_id_to_wal_entries  (cost=0.28..4.61 rows=1 width=0)
        //          Index Cond: (segment_id = ANY ('{c1b28fe7-5572-4d7c-a9aa-8053f9153877,a2b28fe7-5572-4d7c-a9aa-8053f9153878}'::uuid[]))
        //          Filter: (deleted_at < (now() - '24:00:00'::interval))
        // (4 rows)

        if segment_ids.is_empty() {
            return Ok(0);
        }

        let segment_ids_vec: Vec<Uuid> = segment_ids.to_vec();

        let mut query_params: Vec<&(dyn ToSql + Sync)> = Vec::new();
        query_params.push(&segment_ids_vec);
        query_params.push(&expiration_seconds);

        let row = self
            .get_connection()
            .await?
            .query_one(
                format!(
                    r#"SELECT COUNT(*) FROM {}
                    WHERE segment_id = ANY($1::uuid[])
                    AND deleted_at < now() - make_interval(secs => $2::bigint)
                    "#,
                    SEGMENT_ID_TO_WAL_ENTRIES_TABLE,
                )
                .as_str(),
                &query_params,
            )
            .await?;

        Ok(row.get::<_, i64>(0) as u64)
    }

    /// Purge WAL entries that were deleted more than `expiration_seconds` ago.
    async fn purge_deleted_segment_wal_entries(
        &self,
        segment_ids: &[Uuid],
        expiration_seconds: i64,
    ) -> Result<u64> {
        // Query plan:
        //
        //  Delete on brainstore_global_store_segment_id_to_wal_entries  (cost=0.29..14.88 rows=0 width=0)
        //    ->  Index Scan using brainstore_global_store_segment_id__segment_id_wal_filename_idx on brainstore_global_store_segment_id_to_wal_entries  (cost=0.29..14.88 rows=4 width=6)
        //          Index Cond: (segment_id = ANY ('{c1b28fe7-5572-4d7c-a9aa-8053f9153877,a2b28fe7-5572-4d7c-a9aa-8053f9153878}'::uuid[]))
        //          Filter: (deleted_at < (now() - '24:00:00'::interval))
        // (4 rows)

        if segment_ids.is_empty() {
            return Ok(0);
        }

        let segment_ids_vec: Vec<Uuid> = segment_ids.to_vec();

        let mut query_params: Vec<&(dyn ToSql + Sync)> = Vec::new();
        query_params.push(&segment_ids_vec);
        query_params.push(&expiration_seconds);

        Ok(self
            .get_connection()
            .await?
            .execute_raw(
                format!(
                    r#"DELETE FROM {}
                    WHERE segment_id = ANY($1::uuid[])
                    AND deleted_at < now() - make_interval(secs => $2::bigint)
                    "#,
                    SEGMENT_ID_TO_WAL_ENTRIES_TABLE,
                )
                .as_str(),
                query_params.into_iter(),
            )
            .await?)
    }

    /// Restore soft-deleted WAL entries up to the specified xact_id threshold.
    /// This sets `deleted_at = NULL` for entries that were previously soft-deleted
    /// and have xact_id < the specified threshold (exclusive upper bound).
    /// Returns the number of entries restored.
    async fn restore_segment_wal_entries_up_to_xact_id(
        &self,
        segment_ids: &[Uuid],
        xact_id: TransactionId,
    ) -> Result<u64> {
        // Query plan:
        //
        //  Update on brainstore_global_store_segment_id_to_wal_entries  (cost=0.41..9.94 rows=0 width=0)
        //    ->  Index Scan using brainstore_global_store_segment_id_to_wal_entries_pkey on brainstore_global_store_segment_id_to_wal_entries  (cost=0.41..9.94 rows=1 width=14)
        //          Index Cond: ((segment_id = ANY ('{c1b28fe7-5572-4d7c-a9aa-8053f9153877,a2b28fe7-5572-4d7c-a9aa-8053f9153878}'::uuid[])) AND (xact_id < '1000194817061158916'::bigint))
        //          Filter: (deleted_at IS NOT NULL)
        // (4 rows)

        if segment_ids.is_empty() {
            return Ok(0);
        }

        let xact_id_i64: i64 = xact_id.0 as i64;
        let query_params: Vec<&(dyn ToSql + Sync)> = vec![&segment_ids, &xact_id_i64];

        let updated_count = self
            .get_connection()
            .await?
            .execute_raw(
                format!(
                    r#"UPDATE {}
                    SET deleted_at = NULL
                    WHERE segment_id = ANY($1::uuid[])
                    AND deleted_at IS NOT NULL
                    AND xact_id < $2::bigint"#,
                    SEGMENT_ID_TO_WAL_ENTRIES_TABLE,
                )
                .as_str(),
                query_params,
            )
            .await?;

        Ok(updated_count)
    }

    #[instrument(
        err,
        skip(self),
        name = "PostgresGlobalStore::query_vacuum_segment_ids"
    )]
    async fn query_vacuum_segment_ids(
        &self,
        vacuum_type: VacuumType,
        limit: usize,
        max_time_since_last_write: i64,
        min_time_since_last_vacuum: i64,
        start_ts: DateTime<Utc>,
        object_ids: Option<&[FullObjectId<'_>]>,
    ) -> Result<Vec<Uuid>> {
        // Query plan (local):
        //
        // EXPLAIN
        // SELECT segment_id FROM brainstore_global_store_segment_id_to_liveness
        // WHERE is_live
        //     AND vacuum_index_last_successful_start_ts - last_written_ts < make_interval(secs => 3600)
        //     AND vacuum_index_last_successful_start_ts < now() - make_interval(secs => 7200)
        // ORDER BY vacuum_index_last_successful_start_ts - last_written_ts
        // LIMIT 1000;
        //
        // Limit  (cost=0.28..115.87 rows=662 width=32)
        // ->  Index Scan using brainstore_global_store_segment_id_to_liveness_is_live_expr_idx on brainstore_global_store_segment_id_to_liveness  (cost=0.28..115.87 rows=662 width=32)
        //       Index Cond: ((is_live = true) AND ((vacuum_index_last_successful_start_ts - last_written_ts) < '01:00:00'::interval))
        //       Filter: (vacuum_index_last_successful_start_ts < (now() - '02:00:00'::interval))

        time!(self.debug_timer, "query_vacuum_segment_ids");

        let last_successful_start_ts_col = format!("{}_last_successful_start_ts", vacuum_type);
        let object_ids_filter = if object_ids.is_some() {
            "AND object_id = ANY($5::text[])".to_string()
        } else {
            "".to_string()
        };

        let query = format!(
            r#"
            SELECT segment_id FROM {liveness_table}
            WHERE is_live
                  AND {last_successful_start_ts_col} - last_written_ts < make_interval(secs => $1::bigint)
                  AND {last_successful_start_ts_col} < $2::timestamptz - make_interval(secs => $3::bigint)
                  {object_ids_filter}
            ORDER BY {last_successful_start_ts_col} - last_written_ts
            LIMIT $4::bigint
            "#,
            liveness_table = SEGMENT_ID_TO_LIVENESS_TABLE,
            object_ids_filter = object_ids_filter,
            last_successful_start_ts_col = last_successful_start_ts_col,
        );

        let mut params: Vec<&(dyn ToSql + Sync)> = Vec::new();
        let limit_param = limit as i64;
        params.push(&max_time_since_last_write);
        params.push(&start_ts);
        params.push(&min_time_since_last_vacuum);
        params.push(&limit_param);

        let mut _object_ids_param = Vec::new();
        if let Some(object_ids) = object_ids {
            _object_ids_param = object_ids.iter().map(|id| id.to_string()).collect();
            params.push(&_object_ids_param);
        }

        let client = self.pool.get_client().await?;
        let rows = client.query(&query, &params).await?;
        let segment_ids: Vec<Uuid> = rows.iter().map(|row| row.get::<_, Uuid>(0)).collect();

        Ok(segment_ids)
    }

    #[instrument(
        err,
        skip(self),
        name = "PostgresGlobalStore::query_segment_vacuum_state"
    )]
    async fn query_segment_vacuum_state(
        &self,
        segment_ids: &[Uuid],
    ) -> Result<HashMap<Uuid, SegmentVacuumState>> {
        let mut result = HashMap::new();

        let query = format!(
            r#"
            SELECT
                segment_id,
                last_written_ts,
                vacuum_index_last_successful_start_ts,
                vacuum_segment_wal_last_successful_start_ts
            FROM {}
            WHERE segment_id = ANY($1::uuid[])
            "#,
            SEGMENT_ID_TO_LIVENESS_TABLE,
        );

        let rows = self
            .pool
            .get_client()
            .await?
            .query(&query, &[&segment_ids])
            .await?;

        for row in rows {
            let segment_id: Uuid = row.get(0);
            let last_written_ts: DateTime<Utc> = row.get(1);
            let vacuum_index_last_successful_start_ts: DateTime<Utc> = row.get(2);
            let vacuum_segment_wal_last_successful_start_ts: DateTime<Utc> = row.get(3);
            let state = SegmentVacuumState {
                last_written_ts,
                vacuum_index_last_successful_start_ts,
                vacuum_segment_wal_last_successful_start_ts,
            };
            result.insert(segment_id, state);
        }

        Ok(result)
    }

    #[instrument(
        err,
        skip(self),
        name = "PostgresGlobalStore::upsert_segment_vacuum_last_successful_start_ts"
    )]
    async fn upsert_segment_vacuum_last_successful_start_ts(
        &self,
        segment_ids: &[Uuid],
        vacuum_type: VacuumType,
        last_successful_start_ts: DateTime<Utc>,
    ) -> Result<()> {
        time!(
            self.debug_timer,
            "upsert_segment_vacuum_last_successful_start_ts"
        );

        if segment_ids.is_empty() {
            return Ok(());
        }

        let query = format!(
            r#"
            UPDATE {}
            SET
                {}_last_successful_start_ts = $1::timestamptz
            WHERE segment_id = ANY($2::uuid[])
            "#,
            SEGMENT_ID_TO_LIVENESS_TABLE,
            vacuum_type.to_string(),
        );

        let mut params: Vec<&(dyn ToSql + Sync)> = Vec::new();
        params.push(&last_successful_start_ts);
        params.push(&segment_ids);

        let client = self.pool.get_client().await?;
        client.execute(&query, &params).await?;
        Ok(())
    }

    #[instrument(
        err,
        skip(self),
        name = "PostgresGlobalStore::upsert_segment_last_written_ts"
    )]
    async fn upsert_segment_last_written_ts(
        &self,
        segment_ids: &[Uuid],
        last_written_ts: Option<DateTime<Utc>>,
    ) -> Result<()> {
        time!(self.debug_timer, "upsert_segment_last_written_ts");
        if segment_ids.is_empty() {
            return Ok(());
        }

        let mut conn = self.get_connection().await?;
        let mut transaction = conn.transaction().await?;
        self.upsert_segment_last_written_ts_within_tx(
            segment_ids,
            last_written_ts,
            &mut transaction,
        )
        .await?;
        transaction.commit().await?;
        Ok(())
    }

    #[instrument(err, skip(self), name = "PostgresGlobalStore::list_object_ids")]
    async fn list_object_ids(
        &self,
        object_id_cursor: Option<FullObjectId<'_>>,
        limit: usize,
        object_ids_filter: Option<&[FullObjectId<'_>]>,
    ) -> Result<Vec<FullObjectIdOwned>> {
        // Query plan:
        //
        // EXPLAIN
        // SELECT object_id
        // FROM brainstore_global_store_object_id_to_metadata
        // WHERE object_id > 'project_logs:0f6114ae-afa4-4415-92aa-d2d29326a66c'::text
        // ORDER BY object_id
        // LIMIT 1000;
        //
        // Limit  (cost=0.28..29.89 rows=1000 width=48)
        // ->  Index Only Scan using brainstore_global_store_object_id_to_metadata_pkey on
        //       brainstore_global_store_object_id_to_metadata  (cost=0.28..80.97 rows=2725 width=48)
        //       Index Cond: (object_id > 'project_logs:0f6114ae-afa4-4415-92aa-d2d29326a66c'::text)
        //
        // With the object IDs filter:
        //
        // EXPLAIN
        // SELECT object_id
        // FROM brainstore_global_store_object_id_to_metadata
        // WHERE object_id > 'project_logs:0f6114ae-afa4-4415-92aa-d2d29326a66c'::text
        //   AND object_id = ANY(ARRAY[
        //     'project_logs:0f6114ae-afa4-4415-92aa-d2d29326a66c',
        //     'project_logs:1234567-afa4-4415-92aa-d2d29326a66d',
        //     'dataset:abcdefgh-1234-5678-9abc-def123456789'
        //   ]::text[])
        // ORDER BY object_id
        // LIMIT 1000;
        //
        //  Limit  (cost=0.28..4.89 rows=1 width=48)
        // ->  Index Only Scan using brainstore_global_store_object_id_to_metadata_pkey on
        //       brainstore_global_store_object_id_to_metadata  (cost=0.28..4.89 rows=1 width=48)
        //       Index Cond: (
        //           (object_id > 'project_logs:0f6114ae-afa4-4415-92aa-d2d29326a66c'::text)
        //           AND
        //           (object_id = ANY (
        //               '{
        //                   project_logs:0f6114ae-afa4-4415-92aa-d2d29326a66c,
        //                   project_logs:1234567-afa4-4415-92aa-d2d29326a66d,
        //                   dataset:abcdefgh-1234-5678-9abc-def123456789
        //               }'::text[]
        //           ))
        //       )

        time!(self.debug_timer, "list_object_ids");

        let mut params: Vec<&(dyn ToSql + Sync)> = Vec::new();

        let limit_param = limit as i64;
        params.push(&limit_param);

        let mut _cursor: String = String::new();
        let cursor_filter_str = if let Some(cursor) = object_id_cursor {
            _cursor = cursor.to_string();
            params.push(&_cursor);
            "object_id > $2::text"
        } else {
            "true"
        };

        let mut _object_ids_filter_strs = Vec::new();
        let object_ids_filter_str = if let Some(object_ids_filter) = object_ids_filter {
            _object_ids_filter_strs = object_ids_filter.iter().map(|id| id.to_string()).collect();
            params.push(&_object_ids_filter_strs);
            format!("object_id = ANY(${}::text[])", params.len())
        } else {
            "true".to_string()
        };

        let rows = self
            .get_connection()
            .await?
            .query_raw(
                format!(
                    "
                        select object_id
                        from {}
                        where {}
                          and {}
                        order by object_id
                        limit $1
                    ",
                    OBJECT_ID_TO_METADATA_TABLE, cursor_filter_str, object_ids_filter_str,
                )
                .as_str(),
                params,
            )
            .await?;

        rows.map(|row| {
            let id_str: String = row?.get(0);
            Ok(FullObjectIdOwned::from_str(&id_str)?)
        })
        .try_collect()
        .await
    }

    #[instrument(skip(self), name = "PostgresGlobalStore::list_object_segment_ids")]
    async fn list_object_segment_ids(
        &self,
        object_id: FullObjectId<'_>,
        segment_id_cursor: Uuid,
        limit: usize,
    ) -> Result<Vec<Uuid>> {
        // Query plan:
        //
        // EXPLAIN ANALYZE
        // SELECT segment_id
        // FROM brainstore_global_store_segment_id_to_liveness
        // WHERE is_live
        //   AND object_id = 'project_logs:d879f7de-df02-4ecd-9c7a-53ffbee3d4cb'::text
        //   AND segment_id > '0ba85f64-5717-4562-b3fc-2c963f66afa6'::uuid
        // ORDER BY segment_id
        // LIMIT 100;
        //
        // Limit  (cost=0.28..2.30 rows=1 width=16) (actual time=0.026..0.026 rows=0 loops=1)
        // ->  Index Only Scan using brainstore_global_store_segment_id_to__object_id_segment_id_idx
        //       on brainstore_global_store_segment_id_to_liveness  (cost=0.28..2.30 rows=1 width=16)
        //       Index Cond: ((object_id = 'project_logs:d879f7de-df02-4ecd-9c7a-53ffbee3d4cb'::text) AND (segment_id > '0ba85f64-5717-4562-b3fc-2c963f66afa6'::uuid))

        time!(self.debug_timer, "list_object_segment_ids");
        let mut params: Vec<&(dyn ToSql + Sync)> = Vec::new();

        let object_id = object_id.to_string();
        let limit_param = limit as i64;

        params.push(&object_id);
        params.push(&segment_id_cursor);
        params.push(&limit_param);

        let rows = self
            .get_connection()
            .await?
            .query_raw(
                format!(
                    r#"
                    select segment_id
                    from {}
                    where is_live
                    and object_id = $1::text
                    and segment_id > $2::uuid
                    order by segment_id
                    limit $3
                    "#,
                    SEGMENT_ID_TO_LIVENESS_TABLE,
                )
                .as_str(),
                params,
            )
            .await?;

        rows.map(|row| {
            let row = row?;
            let segment_id: Uuid = row.get(0);
            Ok(segment_id)
        })
        .try_collect()
        .await
    }

    #[instrument(skip(self), name = "PostgresGlobalStore::list_object_ids_segment_ids")]
    async fn list_object_ids_segment_ids(
        &self,
        object_ids: &[FullObjectId<'_>],
        limit: usize,
    ) -> Result<Vec<(FullObjectIdOwned, Uuid)>> {
        // Query plan:
        //
        // EXPLAIN
        // SELECT object_id, segment_id
        // FROM brainstore_global_store_segment_id_to_liveness
        // WHERE is_live
        //   AND object_id = ANY(ARRAY[
        //     'project_logs:0f6114ae-afa4-4415-92aa-d2d29326a66c',
        //     'project_logs:1234567-afa4-4415-92aa-d2d29326a66d',
        //     'dataset:abcdefgh-1234-5678-9abc-def123456789'
        //   ]::text[])
        // ORDER BY object_id, segment_id
        // LIMIT 1000;
        //
        // Limit  (cost=0.28..3.90 rows=3 width=64)
        // ->  Index Only Scan using brainstore_global_store_segment_id_to__object_id_segment_id_idx
        //       on brainstore_global_store_segment_id_to_liveness (cost=0.28..3.90 rows=3 width=64)
        //       Index Cond: (object_id = ANY (
        //           '{project_logs:0f6114ae-afa4-4415-92aa-d2d29326a66c,
        //            project_logs:1234567-afa4-4415-92aa-d2d29326a66d,
        //            dataset:abcdefgh-1234-5678-9abc-def123456789}'::text[]
        //       ))

        time!(self.debug_timer, "list_object_ids_segment_ids");
        let mut params: Vec<&(dyn ToSql + Sync)> = Vec::new();

        let object_ids = object_ids
            .iter()
            .map(|id| id.to_string())
            .collect::<Vec<_>>();
        let limit_param = limit as i64;
        params.push(&object_ids);
        params.push(&limit_param);

        let rows = self
            .get_connection()
            .await?
            .query_raw(
                format!(
                    r#"
                    select object_id, segment_id
                    from {}
                    where is_live
                    and object_id = ANY($1::text[])
                    order by object_id, segment_id
                    limit $2
                    "#,
                    SEGMENT_ID_TO_LIVENESS_TABLE,
                )
                .as_str(),
                params,
            )
            .await?;

        rows.map(|row| {
            let row = row?;
            let object_id_str: String = row.get(0);
            let object_id = FullObjectIdOwned::from_str(&object_id_str)?;
            let segment_id: Uuid = row.get(1);
            Ok((object_id, segment_id))
        })
        .try_collect()
        .await
    }

    #[instrument(
        skip(self),
        name = "PostgresGlobalStore::query_time_based_retention_state"
    )]
    async fn query_time_based_retention_state(&self) -> Result<TimeBasedRetentionState> {
        time!(self.debug_timer, "query_time_based_retention_state");

        let conn = self.get_connection().await?;
        let rows = conn
            .query_raw(
                format!(
                    r#"
            select
              last_successful_start_ts,
              current_op_start_ts,
              object_id_cursor,
              segment_id_cursor,
              operation
            from {}
            where id = 'singleton'
            "#,
                    TIME_BASED_RETENTION_STATE_TABLE,
                )
                .as_str(),
                Vec::<&(dyn ToSql + Sync)>::new(),
            )
            .await?;
        pin_mut!(rows);
        let row = match rows.next().await {
            Some(row) => row?,
            None => return Ok(TimeBasedRetentionState::default()),
        };
        match rows.next().await {
            Some(_) => {
                return Err(anyhow!(
                    "Expected at most one row for time-based retention state"
                ))
            }
            None => (),
        }

        let last_successful_start_ts: Option<DateTime<Utc>> = row.get(0);
        let current_op_start_ts: Option<DateTime<Utc>> = row.get(1);
        let object_id_cursor: Option<&str> = row.get(2);
        let segment_id_cursor: Option<Uuid> = row.get(3);
        let operation: Option<serde_json::Value> = row.get(4);

        let cursor = match (object_id_cursor, segment_id_cursor) {
            (Some(object_id_str), Some(segment_id)) => {
                let object_id = FullObjectIdOwned::from_str(&object_id_str)?;
                Some(TimeBasedRetentionCursor {
                    object_id,
                    segment_id,
                })
            }
            (None, None) => None,
            _ => {
                return Err(anyhow!(
                    "Invalid time-based retention state: object_id_cursor and segment_id_cursor must be both present or both null",
                ));
            }
        };

        let operation = match operation {
            Some(v) => serde_json::from_value(v)
                .map_err(|e| anyhow!("Failed to deserialize TimeBasedRetentionOperation: {}", e))?,
            None => TimeBasedRetentionOperation::default(),
        };

        Ok(TimeBasedRetentionState {
            last_successful_start_ts,
            current_op_start_ts,
            cursor,
            operation,
        })
    }

    #[instrument(
        skip(self),
        name = "PostgresGlobalStore::upsert_time_based_retention_state"
    )]
    async fn upsert_time_based_retention_state(
        &self,
        state: &TimeBasedRetentionState,
    ) -> Result<()> {
        time!(self.debug_timer, "upsert_time_based_retention_state");

        let (object_id_cursor, segment_id_cursor) = match &state.cursor {
            Some(cursor) => (Some(cursor.object_id.to_string()), Some(cursor.segment_id)),
            None => (None, None),
        };

        let operation = serde_json::to_value(&state.operation)
            .map_err(|e| anyhow!("Failed to serialize TimeBasedRetentionOperation: {}", e))?;

        let mut query_params: Vec<&(dyn ToSql + Sync)> = Vec::new();
        query_params.push(&state.last_successful_start_ts);
        query_params.push(&state.current_op_start_ts);
        query_params.push(&object_id_cursor);
        query_params.push(&segment_id_cursor);
        query_params.push(&operation);

        let client = self.pool.get_client().await?;
        client
            .execute_raw(
                format!(
                    r#"
                insert into {} (
                    id,
                    last_successful_start_ts,
                    current_op_start_ts,
                    object_id_cursor,
                    segment_id_cursor,
                    operation
                )
                values ('singleton', $1, $2, $3, $4, $5)
                on conflict (id) do update set
                    last_successful_start_ts = excluded.last_successful_start_ts,
                    current_op_start_ts = excluded.current_op_start_ts,
                    object_id_cursor = excluded.object_id_cursor,
                    segment_id_cursor = excluded.segment_id_cursor,
                    operation = excluded.operation
            "#,
                    TIME_BASED_RETENTION_STATE_TABLE,
                )
                .as_str(),
                query_params.into_iter(),
            )
            .await?;
        Ok(())
    }

    async fn query_segment_task_infos(&self, segment_ids: &[Uuid]) -> Result<Vec<TaskInfos>> {
        time!(self.debug_timer, "query_task_infos");

        let client = self.pool.get_client().await?;
        let rows = client
            .query_raw(
                format!(
                    r#"
                        select
                          segment_id,
                          time_based_retention_info,
                          vacuum_index_info,
                          vacuum_segment_wal_info
                        from {}
                        where segment_id = any($1::uuid[])
                    "#,
                    SEGMENT_ID_TO_TASK_INFO_TABLE,
                )
                .as_str(),
                &[segment_ids],
            )
            .await?;

        let mut segment_id_to_task_infos: HashMap<Uuid, TaskInfos> = HashMap::new();

        pin_mut!(rows);
        while let Some(row) = rows.next().await {
            let row = row?;
            let segment_id: Uuid = row.get(0);
            let time_based_retention_info_json: Option<serde_json::Value> = row.get(1);
            let time_based_retention_info: Option<TimeBasedRetentionInfo> =
                time_based_retention_info_json
                    .map(serde_json::from_value)
                    .transpose()?;
            let vacuum_index_info_json: Option<serde_json::Value> = row.get(2);
            let vacuum_index_info: Option<VacuumIndexInfo> = vacuum_index_info_json
                .map(serde_json::from_value)
                .transpose()?;
            let vacuum_segment_wal_info_json: Option<serde_json::Value> = row.get(3);
            let vacuum_segment_wal_info: Option<VacuumSegmentWalInfo> =
                vacuum_segment_wal_info_json
                    .map(serde_json::from_value)
                    .transpose()?;
            segment_id_to_task_infos.insert(
                segment_id,
                TaskInfos {
                    time_based_retention: time_based_retention_info,
                    vacuum_index: vacuum_index_info,
                    vacuum_segment_wal: vacuum_segment_wal_info,
                },
            );
        }

        Ok(segment_ids
            .iter()
            .map(|segment_id| {
                segment_id_to_task_infos
                    .remove(segment_id)
                    .unwrap_or_default()
            })
            .collect())
    }

    async fn upsert_segment_task_info(&self, segment_ids: &[Uuid], info: &TaskInfo) -> Result<()> {
        time!(self.debug_timer, "upsert_task_info");

        let (info_col, info_value) = match info {
            TaskInfo::TimeBasedRetention(retention_info) => (
                "time_based_retention_info",
                serde_json::to_value(retention_info)?,
            ),
            TaskInfo::VacuumIndex(vacuum_index_info) => (
                "vacuum_index_info",
                serde_json::to_value(vacuum_index_info)?,
            ),
            TaskInfo::VacuumSegmentWal(vacuum_segment_wal_info) => (
                "vacuum_segment_wal_info",
                serde_json::to_value(vacuum_segment_wal_info)?,
            ),
        };

        let mut query_params: Vec<&(dyn ToSql + Sync)> = Vec::new();
        query_params.push(&segment_ids);
        query_params.push(&info_value);

        let client = self.pool.get_client().await?;
        client
            .execute_raw(
                format!(
                    r#"
                        insert into {table} (segment_id, {info_col})
                        select unnest($1::uuid[]), $2::jsonb
                        on conflict (segment_id) do update set
                          {info_col} = excluded.{info_col}
                    "#,
                    table = SEGMENT_ID_TO_TASK_INFO_TABLE,
                    info_col = info_col,
                )
                .as_str(),
                query_params.into_iter(),
            )
            .await?;

        Ok(())
    }

    #[instrument(
        err,
        skip(self),
        name = "PostgresGlobalStore::query_unbackfilled_tracking_entries_ordered"
    )]
    async fn query_unbackfilled_tracking_entries_ordered(
        &self,
        has_completed_initial_backfill: bool,
        cursor: Option<BackfillTrackingEntryId<'_>>,
        limit: usize,
    ) -> Result<Vec<BackfillTrackingEntry>> {
        time!(self.debug_timer, "query_ordered_backfill_tracking_entries");

        let mut params: Vec<&(dyn ToSql + Sync)> = Vec::new();
        let limit_i64 = limit as i64;
        params.push(&limit_i64);

        let completed_initial_backfill_clause = if has_completed_initial_backfill {
            "(completed_initial_backfill_ts is not null)"
        } else {
            "(completed_initial_backfill_ts is null)"
        };

        let mut _object_type_str: Option<String> = None;
        let cursor_clause = if let Some(cursor) = &cursor {
            _object_type_str = Some(cursor.object_type.to_string());
            params.push(&cursor.project_id);
            params.push(_object_type_str.as_ref().unwrap());
            format!(
                "(project_id, object_type) > (${}::text, ${}::text)",
                params.len() - 1,
                params.len(),
            )
        } else {
            "true".to_string()
        };

        // Query plan:
        //
        // EXPLAIN
        // SELECT
        //     project_id, object_type, last_processed_sequence_id, last_encountered_sequence_id,
        //     last_processed_sequence_id_2, last_encountered_sequence_id_2,
        //     completed_initial_backfill_ts
        //  FROM brainstore_backfill_tracked_objects
        //  WHERE
        //     is_backfilling
        //     AND completed_initial_backfill_ts is not null
        //     AND (
        //         last_processed_sequence_id < last_encountered_sequence_id
        //         OR last_processed_sequence_id_2 < last_encountered_sequence_id_2
        //     )
        //     AND (project_id, object_type) > ('02bab82f-cf09-4bd4-9974-0fc90c9845d5', 'project_logs')
        //  order by project_id, object_type
        //  limit 100;
        //                                                                              QUERY PLAN
        // ---------------------------------------------------------------------------------------------------------------------------------------------------------------------
        //  Limit  (cost=0.12..96.16 rows=100 width=80)
        //    ->  Index Scan using brainstore_backfill_tracked_objects_project_id_object_type_idx on brainstore_backfill_tracked_objects  (cost=0.12..494.69 rows=515 width=80)
        //          Index Cond: (ROW(project_id, object_type) > ROW('02bab82f-cf09-4bd4-9974-0fc90c9845d5'::text, 'project_logs'::text))
        let rows = self
            .get_connection()
            .await?
            .query_raw(
                format!(
                    r#"
                    SELECT
                        project_id, object_type, last_processed_sequence_id, last_encountered_sequence_id,
                        last_processed_sequence_id_2, last_encountered_sequence_id_2,
                        completed_initial_backfill_ts
                     FROM {}
                     WHERE
                        is_backfilling
                        AND {}
                        AND (
                            last_processed_sequence_id < last_encountered_sequence_id
                            OR last_processed_sequence_id_2 < last_encountered_sequence_id_2
                        )
                        AND {}
                     order by project_id, object_type
                     limit $1
                    "#,
                    BACKFILL_TRACKING_ENTRIES_TABLE, completed_initial_backfill_clause, cursor_clause
                )
                .as_str(),
                params,
            )
            .await?;

        let mut entries = Vec::new();
        pin_mut!(rows);
        while let Some(row) = rows.next().await {
            let row = row?;
            entries.push(BackfillTrackingEntry {
                project_id: row.get(0),
                object_type: ObjectType::from_str(row.get::<_, &str>(1))?,
                last_processed_sequence_id: row.get(2),
                last_encountered_sequence_id: row.get(3),
                last_processed_sequence_id_2: row.get(4),
                last_encountered_sequence_id_2: row.get(5),
                completed_initial_backfill_ts: row.get(6),
            });
        }

        Ok(entries)
    }

    #[instrument(
        err,
        skip(self),
        name = "PostgresGlobalStore::query_backfill_tracking_entries_by_ids"
    )]
    async fn query_backfill_tracking_entries_by_ids(
        &self,
        entries: &[BackfillTrackingEntryId<'_>],
    ) -> Result<Vec<Option<BackfillTrackingEntry>>> {
        time!(self.debug_timer, "query_backfill_tracking_entries_by_ids");

        if entries.is_empty() {
            return Ok(Vec::new());
        }

        let connection = self.get_connection().await?;

        // Collect project_ids and object_types into separate arrays
        let project_ids: Vec<&str> = entries.iter().map(|e| e.project_id).collect();
        let object_types: Vec<String> = entries.iter().map(|e| e.object_type.to_string()).collect();

        // Use unnest to create a table from the arrays and join with the tracking entries table
        //
        // Query plan:
        //
        // EXPLAIN
        // SELECT
        //    input.row_number,
        //    tracking_entries.project_id,
        //    tracking_entries.object_type,
        //    tracking_entries.last_processed_sequence_id,
        //    tracking_entries.last_encountered_sequence_id,
        //    tracking_entries.last_processed_sequence_id_2,
        //    tracking_entries.last_encountered_sequence_id_2,
        //    tracking_entries.completed_initial_backfill_ts
        // FROM
        //    UNNEST(
        //       '{006516fe-5d0b-4f24-b73e-3c5dadcecd27,00e3619f-8e70-488a-9a02-6b5c8a65b2ef,01b58323-a4fd-4665-942d-f3da9c4b6455,01b58323-a4fd-4665-942d-f3da9c4b6455,01e379ec-2220-45b9-a792-df9c305db1f8}'::text[],
        //       '{experiment,dataset,dataset,project_logs,experiment}'::text[]
        //    ) WITH ORDINALITY AS input(project_id, object_type, row_number)
        //    LEFT JOIN brainstore_backfill_tracked_objects tracking_entries ON
        //        input.project_id = tracking_entries.project_id
        //        AND input.object_type = tracking_entries.object_type
        // WHERE
        //    tracking_entries.project_id IS NOT NULL
        //    AND tracking_entries.object_type IS NOT NULL
        //    AND is_backfilling
        // ORDER BY input.row_number;
        //                                                                          QUERY PLAN
        // ------------------------------------------------------------------------------------------------------------------------------------------------------------
        //  Nested Loop  (cost=0.41..12.18 rows=1 width=88)
        //    ->  Function Scan on input  (cost=0.01..0.06 rows=5 width=72)
        //    ->  Index Scan using brainstore_backfill_tracked_objects_pkey on brainstore_backfill_tracked_objects tracking_entries  (cost=0.40..2.42 rows=1 width=80)
        //          Index Cond: ((project_id = input.project_id) AND (project_id IS NOT NULL) AND (object_type = input.object_type) AND (object_type IS NOT NULL))
        //          Filter: is_backfilling
        let query = format!(
            r#"SELECT
                input.row_number,
                tracking_entries.project_id,
                tracking_entries.object_type,
                tracking_entries.last_processed_sequence_id,
                tracking_entries.last_encountered_sequence_id,
                tracking_entries.last_processed_sequence_id_2,
                tracking_entries.last_encountered_sequence_id_2,
                tracking_entries.completed_initial_backfill_ts
             FROM
                UNNEST($1::text[], $2::text[]) WITH ORDINALITY AS input(project_id, object_type, row_number)
                LEFT JOIN {} tracking_entries ON
                    input.project_id = tracking_entries.project_id
                    AND input.object_type = tracking_entries.object_type
             WHERE
                tracking_entries.project_id IS NOT NULL
                AND tracking_entries.object_type IS NOT NULL
                AND is_backfilling
             ORDER BY input.row_number"#,
            BACKFILL_TRACKING_ENTRIES_TABLE
        );

        let rows = connection
            .query(&query, &[&project_ids, &object_types])
            .await?;

        // Initialize result with None values
        let mut result = vec![None; entries.len()];

        // Fill in the results based on row_number
        for row in rows {
            let row_number: i64 = row.get(0);
            let index = (row_number - 1) as usize; // PostgreSQL row_number is 1-indexed
            let entry = BackfillTrackingEntry {
                project_id: row.get(1),
                object_type: ObjectType::from_str(&row.get::<_, String>(2))?,
                last_processed_sequence_id: row.get(3),
                last_encountered_sequence_id: row.get(4),
                last_processed_sequence_id_2: row.get(5),
                last_encountered_sequence_id_2: row.get(6),
                completed_initial_backfill_ts: row.get(7),
            };
            result[index] = Some(entry);
            // If entry doesn't exist, result[index] remains None
        }

        Ok(result)
    }

    #[instrument(
        err,
        skip(self),
        name = "PostgresGlobalStore::update_backfill_tracking_entries"
    )]
    async fn update_backfill_tracking_entries(
        &self,
        updates: Vec<(BackfillTrackingEntryId<'_>, BackfillTrackingEntryUpdate)>,
    ) -> Result<Vec<BackfillTrackingEntry>> {
        time!(self.debug_timer, "update_backfill_tracking_entries");

        if updates.is_empty() {
            return Ok(Vec::new());
        }

        // Create striped arrays for each parameter
        let project_ids: Vec<&str> = updates.iter().map(|(id, _)| id.project_id).collect();
        let object_types: Vec<String> = updates
            .iter()
            .map(|(id, _)| id.object_type.to_string())
            .collect();
        let last_processed_sequence_ids: Vec<Option<i64>> = updates
            .iter()
            .map(|(_, update)| update.last_processed_sequence_id)
            .collect();
        let last_processed_sequence_ids_2: Vec<Option<i64>> = updates
            .iter()
            .map(|(_, update)| update.last_processed_sequence_id_2)
            .collect();
        let num_updates = updates.len() as i64;

        // Query plan:
        // EXPLAIN
        // WITH rows_to_update AS (
        //     SELECT *
        //     FROM unnest(
        //         '{31720175-2866-4bd2-96e8-a5c6b26bef78,d9c01d4f-7fd2-42ea-a468-2a8ccdd8cd16,79b120d7-40d8-4510-bf8a-cf8adb287a66,01b58323-a4fd-4665-942d-f3da9c4b6455,0294ad28-6030-4e94-8ec6-19c036acaff9}'::text[],
        //         '{experiment,dataset,dataset,project_logs,experiment}'::text[],
        //         '{881639,881639,881639,881639,881639}'::bigint[],
        //         '{77525,77525,77525,77525,77525}'::bigint[]
        //     ) with ordinality as t(project_id, object_type, last_processed_sequence_id, last_processed_sequence_id_2, row_num)
        // ),
        // existence_check AS (
        //     SELECT COUNT(*) = 5 as all_exist
        //     FROM rows_to_update r
        //     JOIN brainstore_backfill_tracked_objects b
        //         ON r.project_id = b.project_id
        //         AND r.object_type = b.object_type
        //         AND is_backfilling
        // )
        // UPDATE brainstore_backfill_tracked_objects b
        // SET
        //     last_processed_sequence_id = GREATEST(
        //         r.last_processed_sequence_id,
        //         b.last_processed_sequence_id
        //     ),
        //     last_backfilled_ts = (
        //         case
        //             when r.last_processed_sequence_id > b.last_processed_sequence_id then now()
        //             else b.last_backfilled_ts
        // FROM rows_to_update r
        // WHERE
        //     b.project_id = r.project_id
        //     AND b.object_type = r.object_type
        //     AND (select all_exist from existence_check)
        // RETURNING
        //     r.row_num,
        //     b.project_id,
        //     b.object_type,
        //     b.last_processed_sequence_id,
        //     b.last_processed_sequence_id_2,
        //     b.last_encountered_sequence_id,
        //     b.last_encountered_sequence_id_2,
        //     b.completed_initial_backfill_ts
        // ;
        //                                                                          QUERY PLAN
        // -------------------------------------------------------------------------------------------------------------------------------------------------------------
        //  Update on brainstore_backfill_tracked_objects b  (cost=12.69..24.55 rows=1 width=158)
        //    CTE rows_to_update
        //      ->  Function Scan on t  (cost=0.01..0.06 rows=5 width=88)
        //    InitPlan 2 (returns $3)
        //      ->  Aggregate  (cost=12.21..12.22 rows=1 width=1)
        //            ->  Nested Loop  (cost=0.40..12.20 rows=1 width=0)
        //                  ->  CTE Scan on rows_to_update r_1  (cost=0.00..0.10 rows=5 width=64)
        //                  ->  Index Scan using brainstore_backfill_tracked_objects_pkey on brainstore_backfill_tracked_objects b_1  (cost=0.40..2.42 rows=1 width=48)
        //                        Index Cond: ((project_id = r_1.project_id) AND (object_type = r_1.object_type))
        //                        Filter: is_backfilling
        //    ->  Result  (cost=0.40..12.26 rows=1 width=158)
        //          One-Time Filter: $3
        //          ->  Nested Loop  (cost=0.40..12.25 rows=1 width=174)
        //                ->  CTE Scan on rows_to_update r  (cost=0.00..0.10 rows=5 width=200)
        //                ->  Index Scan using brainstore_backfill_tracked_objects_pkey on brainstore_backfill_tracked_objects b  (cost=0.40..2.42 rows=1 width=86)
        //                      Index Cond: ((project_id = r.project_id) AND (object_type = r.object_type))
        let query = format!(
            r#"
            WITH rows_to_update AS (
                SELECT *
                FROM unnest(
                    $1::text[],
                    $2::text[],
                    $3::bigint[],
                    $4::bigint[]
                ) with ordinality as t(project_id, object_type, last_processed_sequence_id, last_processed_sequence_id_2, row_num)
            ),
            existence_check AS (
                SELECT COUNT(*) = $5 as all_exist
                FROM rows_to_update r
                JOIN {} b
                    ON r.project_id = b.project_id
                    AND r.object_type = b.object_type
                    AND is_backfilling
            )
            UPDATE {} b
            SET
                last_processed_sequence_id = GREATEST(
                    r.last_processed_sequence_id,
                    b.last_processed_sequence_id
                ),
                last_backfilled_ts = (
                    case
                        when r.last_processed_sequence_id > b.last_processed_sequence_id then now()
                        else b.last_backfilled_ts
                    end
                ),
                last_processed_sequence_id_2 = GREATEST(
                    r.last_processed_sequence_id_2,
                    b.last_processed_sequence_id_2
                ),
                last_backfilled_ts_2 = (
                    case
                        when r.last_processed_sequence_id_2 > b.last_processed_sequence_id_2 then now()
                        else b.last_backfilled_ts_2
                    end
                )
            FROM rows_to_update r
            WHERE
                b.project_id = r.project_id
                AND b.object_type = r.object_type
                AND (select all_exist from existence_check)
            RETURNING
                r.row_num,
                b.project_id,
                b.object_type,
                b.last_processed_sequence_id,
                b.last_processed_sequence_id_2,
                b.last_encountered_sequence_id,
                b.last_encountered_sequence_id_2,
                b.completed_initial_backfill_ts
        "#,
            BACKFILL_TRACKING_ENTRIES_TABLE, BACKFILL_TRACKING_ENTRIES_TABLE
        );

        let query_params: Vec<&(dyn ToSql + Sync)> = vec![
            &project_ids,
            &object_types,
            &last_processed_sequence_ids,
            &last_processed_sequence_ids_2,
            &num_updates,
        ];

        let row_stream = self
            .get_connection()
            .await?
            .query_raw(&query, query_params)
            .await?;
        pin_mut!(row_stream);

        let mut out = vec![BackfillTrackingEntry::default(); num_updates as usize];
        let mut num_updated_rows = 0;
        while let Some(row) = row_stream.next().await {
            let row = row?;
            let row_num: i64 = row.get(0);
            out[(row_num - 1) as usize] = BackfillTrackingEntry {
                project_id: row.get(1),
                object_type: row.get::<usize, String>(2).parse()?,
                last_processed_sequence_id: row.get(3),
                last_processed_sequence_id_2: row.get(4),
                last_encountered_sequence_id: row.get(5),
                last_encountered_sequence_id_2: row.get(6),
                completed_initial_backfill_ts: row.get(7),
            };
            num_updated_rows += 1;
        }

        if num_updated_rows != num_updates as u64 {
            assert_eq!(num_updated_rows, 0);
            return Err(anyhow!("One or more tracking entries do not exist. Possibly they were deleted or marked not backfilling"));
        }

        Ok(out)
    }

    #[instrument(
        err,
        skip(self),
        name = "PostgresGlobalStore::query_backfill_brainstore_objects"
    )]
    async fn query_backfill_brainstore_objects(
        &self,
        tracking_entries: &[BackfillTrackingEntryId<'_>],
        is_logs2: bool,
        min_sequence_id: i64,
        max_sequence_id: i64,
    ) -> Result<Vec<Vec<BackfillBrainstoreObject>>> {
        time!(self.debug_timer, "query_backfill_brainstore_objects");

        if tracking_entries.is_empty() || min_sequence_id > max_sequence_id {
            return Ok(Vec::new());
        }

        let mut entry_to_output_ind: HashMap<BackfillTrackingEntryId, usize> = HashMap::new();
        for (i, entry) in tracking_entries.iter().enumerate() {
            entry_to_output_ind.insert(entry.clone(), i);
        }

        // Query the correct logs table to get the objects that are in the range
        // of sequence IDs.
        let tbl = if is_logs2 { LOGS2_TABLE } else { LOGS_TABLE };

        // Query plan:
        // explain
        // select
        //     make_object_id(project_id, experiment_id, dataset_id, prompt_session_id, log_id) object_id,
        //     min(sequence_id)::bigint min_sequence_id,
        //     max(sequence_id)::bigint max_sequence_id,
        //     min(_xact_id) min_xact_id,
        //     max(_xact_id) max_xact_id,
        //     min(project_id) project_id,
        //     min(experiment_id) experiment_id,
        //     min(dataset_id) dataset_id,
        //     min(prompt_session_id) prompt_session_id,
        //     min(log_id) log_id
        // from logs
        // where
        //     sequence_id >= 871646
        //     and sequence_id <= 881646
        //     and make_object_id(project_id, experiment_id, dataset_id, prompt_session_id, log_id) is not null
        //     and project_id is not null
        // group by object_id;
        //                                                                                                                                                                                 QUERY PLAN
        // ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
        //  HashAggregate  (cost=242.87..275.59 rows=935 width=224)
        //    Group Key: COALESCE(('experiment:'::text || experiment_id), ('dataset:'::text || dataset_id), ('prompt_session:'::text || prompt_session_id), CASE WHEN (log_id = 'g'::text) THEN ('global_log:'::text || project_id) WHEN (log_id = 'p'::text) THEN ('prompt:'::text || project_id) ELSE ('log:'::text || log_id) END)
        //    ->  Index Scan using logs_pkey on logs  (cost=0.42..213.07 rows=1192 width=195)
        //          Index Cond: ((sequence_id >= 871646) AND (sequence_id <= 881646))
        //          Filter: ((project_id IS NOT NULL) AND (COALESCE(('experiment:'::text || experiment_id), ('dataset:'::text || dataset_id), ('prompt_session:'::text || prompt_session_id), CASE WHEN (log_id = 'g'::text) THEN ('global_log:'::text || project_id) WHEN (log_id = 'p'::text) THEN ('prompt:'::text || project_id) ELSE ('log:'::text || log_id) END) IS NOT NULL))
        let query_text = format!(
            r#"
            select
                {} object_id,
                min(sequence_id)::bigint min_sequence_id,
                max(sequence_id)::bigint max_sequence_id,
                min(_xact_id) min_xact_id,
                max(_xact_id) max_xact_id,
                min(project_id) project_id,
                min(experiment_id) experiment_id,
                min(dataset_id) dataset_id,
                min(prompt_session_id) prompt_session_id,
                min(log_id) log_id
            from {}
            where
                sequence_id >= $1
                and sequence_id <= $2
                and {} is not null
                and project_id is not null
            group by object_id
            "#,
            make_object_id_column_expr(tbl),
            tbl,
            make_object_id_column_expr(tbl)
        );

        let mut query_params: Vec<&(dyn ToSql + Sync)> = Vec::new();
        let mut _min_sequence_id_i32: Option<i32> = None;
        let mut _max_sequence_id_i32: Option<i32> = None;
        if is_logs2 {
            query_params.push(&min_sequence_id);
            query_params.push(&max_sequence_id);
        } else {
            _min_sequence_id_i32 = Some(i32::try_from(min_sequence_id)?);
            _max_sequence_id_i32 = Some(i32::try_from(max_sequence_id)?);
            query_params.push(_min_sequence_id_i32.as_ref().unwrap());
            query_params.push(_max_sequence_id_i32.as_ref().unwrap());
        }
        let rows = self
            .get_connection()
            .await?
            .query_raw(query_text.as_str(), query_params)
            .await?;
        pin_mut!(rows);
        let mut results: Vec<Vec<BackfillBrainstoreObject>> =
            vec![Vec::new(); tracking_entries.len()];
        while let Some(row) = rows.next().await {
            let row = row?;
            let min_sequence_id: i64 = row.get(1);
            let max_sequence_id: i64 = row.get(2);
            let min_xact_id: i64 = row.get(3);
            let max_xact_id: i64 = row.get(4);
            if min_xact_id < 0 || max_xact_id < 0 {
                log::warn!(
                    "Negative xact_id found in table {} between sequence IDs {} and {}. Skipping object.",
                    tbl,
                    min_sequence_id,
                    max_sequence_id
                );
                continue;
            }
            let min_xact_id = TransactionId(min_xact_id as u64);
            let max_xact_id = TransactionId(max_xact_id as u64);
            let project_id: &str = row.get(5);
            let postgres_object_id_fields = PostgresObjectIdFields {
                project_id: Some(project_id),
                experiment_id: row.get(6),
                dataset_id: row.get(7),
                prompt_session_id: row.get(8),
                log_id: row.get(9),
            };
            let object_id = match make_brainstore_object_id(postgres_object_id_fields)? {
                Some(id) => id,
                None => {
                    continue;
                }
            };

            if let Some(output_ind) = entry_to_output_ind.get(&BackfillTrackingEntryId {
                project_id,
                object_type: object_id.object_type,
            }) {
                results[*output_ind].push(BackfillBrainstoreObject {
                    project_id: project_id.to_owned(),
                    object_id: object_id.to_owned(),
                    is_logs2,
                    min_sequence_id,
                    max_sequence_id,
                    min_xact_id,
                    max_xact_id,
                });
            }
        }

        Ok(results)
    }

    #[cfg(test)]
    #[instrument(
        err,
        skip(self),
        name = "PostgresGlobalStore::testing_only_insert_backfill_data"
    )]
    async fn testing_only_insert_backfill_data(
        &self,
        tracking_entries: Vec<BackfillTrackingEntry>,
        brainstore_objects: Vec<crate::global_store::TestingOnlyBackfillBrainstoreObjectAtom>,
        inject_dummy_row_refs: bool,
    ) -> Result<()> {
        // Note: this implementation doesn't have to be very efficient because
        // it's only used for testing.

        use crate::postgres_query_util::make_postgres_object_id_fields;
        use crate::row_ref::RowRef;
        time!(self.debug_timer, "testing_only_insert_backfill_data");

        let mut client = self.get_connection().await?;
        let transaction = client.transaction().await?;

        // Insert tracking entries
        for entry in tracking_entries {
            let object_type_str = entry.object_type.to_string();
            transaction
                .execute(
                    &format!("INSERT INTO {}
                     (project_id, object_type, last_processed_sequence_id, last_encountered_sequence_id,
                      last_processed_sequence_id_2, last_encountered_sequence_id_2, is_backfilling, completed_initial_backfill_ts,
                      initial_sequence_id, initial_sequence_id_2)
                     VALUES ($1, $2, $3, $4, $5, $6, true, $7, 0, 0)", BACKFILL_TRACKING_ENTRIES_TABLE),
                    &[
                        &entry.project_id,
                        &object_type_str,
                        &entry.last_processed_sequence_id,
                        &entry.last_encountered_sequence_id,
                        &entry.last_processed_sequence_id_2,
                        &entry.last_encountered_sequence_id_2,
                        &entry.completed_initial_backfill_ts,
                    ],
                )
                .await?;
        }

        // Insert brainstore objects
        for object in brainstore_objects {
            let postgres_object_id_fields =
                make_postgres_object_id_fields(&object.object_id.as_ref()).ok_or_else(|| {
                    anyhow!(
                        "Object ID is not brainstore-compatible: {}",
                        object.object_id
                    )
                })?;
            let mut data_record = util::serde_json::json!({
                "project_id": object.project_id,
                "experiment_id": postgres_object_id_fields.experiment_id,
                "dataset_id": postgres_object_id_fields.dataset_id,
                "prompt_session_id": postgres_object_id_fields.prompt_session_id,
                "log_id": postgres_object_id_fields.log_id,
                "id": Uuid::new_v4().to_string(),
                "_xact_id": object.xact_id.to_string(),
            });
            if inject_dummy_row_refs {
                let dummy_row_ref = RowRef {
                    key: "nonexistent_file.jsonl".to_string(),
                    byte_range_start: 0,
                    byte_range_end: 100,
                };
                data_record["_row_ref"] = util::serde_json::to_value(dummy_row_ref)?;
            }
            transaction
                .execute(
                    &format!(
                        "INSERT INTO {}(sequence_id, data) values ($1::bigint, $2::jsonb)",
                        if object.is_logs2 {
                            LOGS2_TABLE
                        } else {
                            LOGS_TABLE
                        }
                    ),
                    &[&object.sequence_id, &data_record],
                )
                .await?;
        }

        transaction.commit().await?;
        Ok(())
    }

    async fn status(&self) -> Result<String> {
        let conn = self.get_connection().await?;
        validate_postgres_connection(conn).await?;
        Ok("PostgresGlobalStore is ok".into())
    }
}

impl PostgresGlobalStore {
    async fn upsert_segment_metadatas_within_tx(
        &self,
        updates: HashMap<Uuid, SegmentMetadataUpdate>,
        transaction: &mut deadpool_postgres::Transaction<'_>,
    ) -> Result<bool> {
        if updates.is_empty() {
            return Ok(true);
        }

        // First grab all existing rows matching any of the given segment IDs, grabbing a write
        // lock on any matching rows.
        let update_segment_ids = updates.keys().collect::<Vec<_>>();
        let rows = transaction
            .query_raw(
                &format!(
                    r#"
                    select segment_id, last_compacted_index_meta_xact_id, last_compacted_index_meta_tantivy_meta, minimum_pagination_key, num_rows
                    from {} where segment_id = any($1::uuid[])
                    for update
                "#,
                    SEGMENT_ID_TO_METADATA_TABLE,
                ),
                &[&update_segment_ids],
            )
            .await?;
        let segment_id_to_existing_metadata: HashMap<Uuid, SegmentMetadata> = rows
            .map(|row| -> Result<(Uuid, SegmentMetadata)> { get_segment_id_and_metadata(&row?) })
            .try_collect()
            .await?;

        // Next, do the CAS checks between any existing metadatas and the updates.
        for (segment_id, update) in &updates {
            if let Some(existing_metadata) = segment_id_to_existing_metadata.get(segment_id) {
                if let Some((prev_index_meta, _)) = &update.last_compacted_index_meta {
                    if existing_metadata.last_compacted_index_meta != *prev_index_meta {
                        return Ok(false);
                    }
                }
                if let Some((prev_pagination_key, _)) = update.minimum_pagination_key {
                    if existing_metadata.minimum_pagination_key != prev_pagination_key {
                        return Ok(false);
                    }
                }
            }
        }

        // Prepare a list of updated row values for an upsert operation.
        let updated_metadatas = {
            let mut out: HashMap<Uuid, PostgresSegmentMetadata> = HashMap::new();
            for (segment_id, update) in updates.iter() {
                let mut new_metadata = segment_id_to_existing_metadata
                    .get(segment_id)
                    .cloned()
                    .unwrap_or_default();
                if let Some((_, last_compacted_index_meta)) = &update.last_compacted_index_meta {
                    new_metadata.last_compacted_index_meta = last_compacted_index_meta.clone();
                }
                if let Some((_, pagination_key)) = update.minimum_pagination_key {
                    new_metadata.minimum_pagination_key = pagination_key;
                }
                if let Some(num_rows) = update.add_num_rows {
                    new_metadata.num_rows += num_rows;
                }
                out.insert(
                    *segment_id,
                    PostgresSegmentMetadata::from_segment_metadata(new_metadata),
                );
            }
            out
        };
        let mut query_params: Vec<&(dyn ToSql + Sync)> = Vec::new();
        for segment_id in updates.keys() {
            let metadata = updated_metadatas.get(segment_id).unwrap();
            query_params.push(segment_id);
            query_params.push(&metadata.last_compacted_index_meta_xact_id);
            query_params.push(&metadata.last_compacted_index_meta_tantivy_meta);
            query_params.push(&metadata.minimum_pagination_key);
            query_params.push(&metadata.num_rows);
        }
        let placeholders: Vec<String> = (0..(query_params.len() / 5))
            .map(|chunk_idx| {
                let start = chunk_idx * 5 + 1;
                format!(
                    "(${}::uuid, ${}::bigint, ${}::text, ${}::numeric(20, 0), ${}::bigint)",
                    start,
                    start + 1,
                    start + 2,
                    start + 3,
                    start + 4,
                )
            })
            .collect();
        let num_rows_modified = transaction
            .execute_raw(
                &format!(
                    r#"
                    insert into {}(segment_id, last_compacted_index_meta_xact_id, last_compacted_index_meta_tantivy_meta, minimum_pagination_key, num_rows)
                    values {}
                    on conflict (segment_id) do update set
                        last_compacted_index_meta_xact_id = excluded.last_compacted_index_meta_xact_id,
                        last_compacted_index_meta_tantivy_meta = excluded.last_compacted_index_meta_tantivy_meta,
                        minimum_pagination_key = excluded.minimum_pagination_key,
                        num_rows = excluded.num_rows
                "#,
                    SEGMENT_ID_TO_METADATA_TABLE,
                    placeholders.join(", "),
                ),
                query_params.into_iter(),
            )
            .await?;
        if num_rows_modified != updates.len() as u64 {
            return Err(anyhow!("failed to update all segment metadatas"));
        }
        Ok(true)
    }

    async fn upsert_field_statistics_within_tx(
        &self,
        segment_id_field_name_statistics: Vec<(Uuid, &str, SegmentFieldStatistics)>,
        transaction: &mut deadpool_postgres::Transaction<'_>,
    ) -> Result<()> {
        if segment_id_field_name_statistics.is_empty() {
            return Ok(());
        }

        let postgres_field_statistics = segment_id_field_name_statistics
            .into_iter()
            .map(|(segment_id, field_name, stats)| {
                (
                    segment_id,
                    field_name,
                    PostgresSegmentFieldStatistics::from_field_statistics(stats),
                )
            })
            .collect::<Vec<_>>();

        let mut query_params: Vec<&(dyn ToSql + Sync)> = Vec::new();
        let mut values = Vec::new();

        for (segment_id, field_name, stats) in &postgres_field_statistics {
            query_params.push(segment_id);
            query_params.push(field_name);
            query_params.push(&stats.min_u64);
            query_params.push(&stats.max_u64);
            values.push(format!(
                "(${}::uuid, ${}::text, ${}::numeric(20, 0), ${}::numeric(20, 0))",
                query_params.len() - 3,
                query_params.len() - 2,
                query_params.len() - 1,
                query_params.len()
            ));
        }

        transaction
            .execute_raw(
                format!(
                    r#"
                INSERT INTO brainstore_global_store_segment_id_to_column_statistics
                    (segment_id, field_name, min_u64, max_u64)
                VALUES {}
                ON CONFLICT (segment_id, field_name) DO UPDATE SET
                    min_u64 = EXCLUDED.min_u64,
                    max_u64 = EXCLUDED.max_u64
                "#,
                    values.join(",")
                )
                .as_str(),
                query_params.into_iter(),
            )
            .await?;
        Ok(())
    }

    async fn upsert_segment_last_written_ts_within_tx(
        &self,
        segment_ids: &[Uuid],
        last_written_ts: Option<DateTime<Utc>>,
        transaction: &mut deadpool_postgres::Transaction<'_>,
    ) -> Result<()> {
        if segment_ids.is_empty() {
            return Ok(());
        }

        let params: Vec<&(dyn ToSql + Sync)> = vec![&last_written_ts, &segment_ids];

        transaction
            .execute_raw(
                format!(
                    r#"
                    UPDATE {}
                    SET last_written_ts = COALESCE($1::timestamptz, NOW())
                    WHERE segment_id = ANY($2::uuid[])
                "#,
                    SEGMENT_ID_TO_LIVENESS_TABLE,
                )
                .as_str(),
                params.into_iter(),
            )
            .await?;
        Ok(())
    }
}

impl Instrumented for PostgresGlobalStore {
    fn enable_timing(&self) {
        self.debug_timer.enable_granular_timing();
    }

    fn reset_timing(&self) {
        self.debug_timer.reset();
    }

    fn timers(&self) -> Vec<Arc<TimerManager>> {
        vec![self.debug_timer.clone()]
    }
}

fn get_u64(row: &tokio_postgres::Row, idx: usize) -> Result<u64> {
    let val: Decimal = row.get(idx);
    val.to_u64()
        .ok_or_else(|| anyhow!("value is not representable as a u64"))
}

fn get_i64(row: &tokio_postgres::Row, idx: usize) -> i64 {
    row.get(idx)
}

fn get_xact_id(row: &tokio_postgres::Row, idx: usize) -> TransactionId {
    TransactionId(get_i64(row, idx) as u64)
}

fn get_opt_xact_id(row: &tokio_postgres::Row, idx: usize) -> Option<TransactionId> {
    let val: Option<i64> = row.get(idx);
    val.map(|x| TransactionId(x as u64))
}

fn get_segment_id_and_metadata(row: &tokio_postgres::Row) -> Result<(Uuid, SegmentMetadata)> {
    let segment_id: Uuid = row.get(0);
    let last_compacted_xact_id = get_opt_xact_id(row, 1);
    let last_compacted_index_meta_tantivy_meta: Option<IndexMetaJson> = {
        let json_str: Option<String> = row.get(2);
        json_str
            .map(|json_str| serde_json::from_str(&json_str))
            .transpose()
    }?;
    let last_compacted_index_meta = match (
        last_compacted_xact_id,
        last_compacted_index_meta_tantivy_meta,
    ) {
        (None, None) => None,
        (Some(_), None) | (None, Some(_)) => {
            return Err(anyhow!(
                "missing last_compacted_index_meta_xact_id or last_compacted_index_meta_tantivy_meta"
            ));
        }
        (Some(xact_id), Some(tantivy_meta)) => Some(LastCompactedIndexMeta {
            xact_id,
            tantivy_meta,
        }),
    };
    let metadata = SegmentMetadata {
        last_compacted_index_meta,
        minimum_pagination_key: PaginationKey(get_u64(row, 3)?),
        num_rows: row.get::<usize, i64>(4) as u64,
    };
    Ok((segment_id, metadata))
}

struct PostgresSegmentMetadata {
    pub last_compacted_index_meta_xact_id: Option<i64>,
    pub last_compacted_index_meta_tantivy_meta: Option<String>,
    pub minimum_pagination_key: Decimal,
    pub num_rows: i64,
}

impl PostgresSegmentMetadata {
    pub fn from_segment_metadata(metadata: SegmentMetadata) -> Self {
        Self {
            last_compacted_index_meta_xact_id: metadata
                .last_compacted_index_meta
                .as_ref()
                .map(|x| x.xact_id.0 as i64),
            last_compacted_index_meta_tantivy_meta: metadata
                .last_compacted_index_meta
                .as_ref()
                .map(|x| serde_json::to_string(&x.tantivy_meta).unwrap()),
            minimum_pagination_key: Decimal::from(metadata.minimum_pagination_key.0),
            num_rows: metadata.num_rows as i64,
        }
    }
}

fn get_object_id_and_metadata(row: &tokio_postgres::Row) -> Result<(String, ObjectMetadata)> {
    let object_id: String = row.get(0);
    let last_processed_xact_id = get_opt_xact_id(row, 1);
    let metadata = ObjectMetadata {
        last_processed_xact_id,
        wal_token: Default::default(),
    };
    Ok((object_id, metadata))
}

struct PostgresObjectMetadata {
    pub last_processed_xact_id: Option<i64>,
}

impl PostgresObjectMetadata {
    pub fn from_object_metadata(metadata: ObjectMetadata) -> Self {
        Self {
            last_processed_xact_id: metadata.last_processed_xact_id.as_ref().map(|x| x.0 as i64),
        }
    }
}

#[derive(Debug, Default, Clone)]
struct PostgresSegmentFieldStatistics {
    min_u64: Option<Decimal>,
    max_u64: Option<Decimal>,
}

impl PostgresSegmentFieldStatistics {
    fn from_row(row: &tokio_postgres::Row) -> Result<Self> {
        Ok(Self {
            min_u64: row.get(2),
            max_u64: row.get(3),
        })
    }

    fn from_field_statistics(stats: SegmentFieldStatistics) -> Self {
        Self {
            min_u64: Some(Decimal::from(stats.min())),
            max_u64: Some(Decimal::from(stats.max())),
        }
    }

    fn field_statistics(&self) -> Result<Option<SegmentFieldStatistics>> {
        match (self.min_u64, self.max_u64) {
            (Some(min), Some(max)) => Ok(Some(SegmentFieldStatistics::new(
                min.to_u64()
                    .ok_or_else(|| anyhow!("min is not representable as u64"))?,
                max.to_u64()
                    .ok_or_else(|| anyhow!("max is not representable as u64"))?,
            )?)),
            (None, None) => Ok(None),
            _ => Err(anyhow!("Missing min or max value")),
        }
    }
}

pub struct PoolMetrics {
    // Percent of connections in use
    pub connections_util: Gauge<f64>,
    // Percent of long-lived connections in use
    pub long_lived_connections_util: Gauge<f64>,
    pub pool_size: Gauge<u64>,
    // PostgreSQL connection states
    pub pg_connections_active: Gauge<u64>,
    pub pg_connections_idle: Gauge<u64>,
    pub pg_connections_idle_in_transaction: Gauge<u64>,
    pub pg_connections_idle_in_transaction_aborted: Gauge<u64>,
    // Connection lifecycle metrics
    pub connection_wait_time_ms: Histogram<u64>,
    pub pool_exhaustion_count: Counter<u64>,
    pub connection_age_histogram: Histogram<u64>,
    pub oldest_connection_seconds: Gauge<u64>,
    // Long-running query metrics
    pub long_running_queries_count: Gauge<u64>,
    pub longest_query_duration_seconds: Gauge<u64>,
    pub query_duration_histogram: Histogram<u64>,
}

impl Default for PoolMetrics {
    fn default() -> Self {
        let meter = global::meter("brainstore");
        Self {
            connections_util: meter
                .f64_gauge("brainstore.global_store.connections")
                .build(),
            long_lived_connections_util: meter
                .f64_gauge("brainstore.global_store.long_lived_connections")
                .build(),
            pool_size: meter.u64_gauge("brainstore.global_store.pool_size").build(),
            // PostgreSQL connection states
            pg_connections_active: meter.u64_gauge("brainstore.pg_connections.active").build(),
            pg_connections_idle: meter.u64_gauge("brainstore.pg_connections.idle").build(),
            pg_connections_idle_in_transaction: meter
                .u64_gauge("brainstore.pg_connections.idle_in_transaction")
                .build(),
            pg_connections_idle_in_transaction_aborted: meter
                .u64_gauge("brainstore.pg_connections.idle_in_transaction_aborted")
                .build(),
            // Connection lifecycle metrics
            connection_wait_time_ms: meter
                .u64_histogram("brainstore.connection.wait_time_ms")
                .build(),
            pool_exhaustion_count: meter
                .u64_counter("brainstore.pool.exhaustion_count")
                .build(),
            connection_age_histogram: meter
                .u64_histogram("brainstore.connection.age_seconds")
                .build(),
            oldest_connection_seconds: meter
                .u64_gauge("brainstore.connection.oldest_seconds")
                .build(),
            // Long-running query metrics
            long_running_queries_count: meter
                .u64_gauge("brainstore.query.long_running_count")
                .with_description("Number of queries running longer than 1 minute")
                .build(),
            longest_query_duration_seconds: meter
                .u64_gauge("brainstore.query.longest_duration_seconds")
                .with_description("Duration of the longest running query in seconds")
                .build(),
            query_duration_histogram: meter
                .u64_histogram("brainstore.query.duration_seconds")
                .with_description("Histogram of query durations for active queries")
                .build(),
        }
    }
}

impl PoolMetrics {
    pub fn record(&self) {
        let pool_size = global_limits().global_store_pool_size;
        self.pool_size.record(pool_size as u64, &[]);
        if let Ok(pools) = GLOBAL_STORE_POOLS.try_lock() {
            for (url, pool) in pools.iter() {
                let status = pool.status();
                self.connections_util.record(
                    (status.size as f64) / (status.max_size as f64),
                    &[KeyValue::new(
                        "host",
                        url.host()
                            .map_or_else(|| "unknown".to_string(), |host| host.to_string()),
                    )],
                );
            }
        }
    }
}

lazy_static! {
    pub static ref POOL_METRICS: PoolMetrics = PoolMetrics::default();
}

pub fn record_pool_metrics() {
    POOL_METRICS.record();
}

async fn query_id_segment_membership_process_stream(
    stream: tokio_postgres::RowStream,
    is_full_id: bool,
    id_to_full_row_ids: &HashMap<&str, Vec<FullRowId<'_, '_>>>,
) -> Result<Vec<(FullRowIdOwned, Uuid)>> {
    pin_mut!(stream);
    let mut out = Vec::new();
    while let Some(row) = stream.next().await {
        let row = row?;
        let segment_id: Uuid = row.get(1);
        if is_full_id {
            let id: FullRowIdOwned = row.get::<usize, String>(0).parse()?;
            out.push((id, segment_id));
        } else {
            let id: &str = row.get(0);
            if let Some(full_row_ids) = id_to_full_row_ids.get(id) {
                for full_row_id in full_row_ids {
                    out.push((full_row_id.to_owned(), segment_id));
                }
            }
        };
    }
    Ok::<_, util::anyhow::Error>(out)
}
